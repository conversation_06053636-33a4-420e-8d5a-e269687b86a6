# Database Permissions System Improvement Plan

## Executive Summary

This document outlines a comprehensive plan to restructure the appraisal tool's database and permissions system. The current implementation has significant architectural issues causing identity confusion, permission inconsistencies, and maintenance challenges. This plan proposes a phased approach to create a unified, scalable, and maintainable system.

## Current State Analysis

### 1. Identity Crisis

The system currently uses two different identity systems:
- **Clerk User IDs** (text): External authentication system IDs used in `appy_managers`
- **UUIDs**: Internal system IDs used in `appy_employees`

This dual-identity system creates:
- Complex foreign key relationships
- Inconsistent RLS (Row Level Security) policies  
- Difficulty in tracking user actions
- Confusion in API endpoints and business logic

### 2. Role System Fragmentation

Current role management issues:
- Roles only exist in the `appy_managers` table
- Employees have no direct role assignment
- Dual role fields: `role` (enum) + `additional_roles` (JSONB)
- The `get_user_role()` function ignores additional roles
- No role history or audit trail

### 3. Permission System Weaknesses

Current permission problems:
- RLS policies use `auth.uid()` which returns Clerk IDs
- Cannot properly protect employee data (UUID-based)
- Oversimplified `is_admin()` function
- No granular permissions
- No department-based access control
- No delegation or temporary permissions

### 4. Data Relationship Issues

Relationship problems include:
- `appy_employee_managers` uses text `manager_id` (Clerk ID)
- `appy_employees` has duplicate `manager_id` field
- Mixed ID types require complex joins
- No proper hierarchy traversal
- Circular reference possibilities

## Proposed Solution Architecture

### Phase 1: Unified User System

#### 1.1 New Core Tables

**appy_users** - Single source of truth for all users
```sql
CREATE TABLE appy_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  clerk_id TEXT UNIQUE NOT NULL,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT NOT NULL,
  first_name TEXT,
  last_name TEXT,
  employee_id TEXT UNIQUE, -- For external HR system reference
  department_id UUID REFERENCES appy_departments(id),
  compensation NUMERIC(10,2),
  compensation_rate appy_compensation_rate DEFAULT 'monthly',
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  metadata JSONB DEFAULT '{}'::jsonb
);

CREATE INDEX idx_users_clerk_id ON appy_users(clerk_id);
CREATE INDEX idx_users_email ON appy_users(email);
CREATE INDEX idx_users_department ON appy_users(department_id);
```

**appy_user_roles** - Role assignments with history
```sql
CREATE TABLE appy_user_roles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES appy_users(id) ON DELETE CASCADE,
  role appy_user_role NOT NULL,
  department_id UUID REFERENCES appy_departments(id),
  scope_type TEXT CHECK (scope_type IN ('global', 'department', 'team')),
  scope_id UUID, -- References department or team based on scope_type
  effective_from TIMESTAMPTZ DEFAULT now(),
  effective_to TIMESTAMPTZ,
  granted_by UUID REFERENCES appy_users(id),
  reason TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  CONSTRAINT valid_date_range CHECK (effective_to IS NULL OR effective_to > effective_from)
);

CREATE INDEX idx_user_roles_user ON appy_user_roles(user_id);
CREATE INDEX idx_user_roles_active ON appy_user_roles(user_id, effective_from, effective_to);
```

#### 1.2 Permission System Tables

**appy_permissions** - Define all system permissions
```sql
CREATE TABLE appy_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code TEXT UNIQUE NOT NULL, -- e.g., 'employee:write'
  resource TEXT NOT NULL,    -- e.g., 'employee'
  action TEXT NOT NULL,      -- e.g., 'write'
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Seed with current permissions
INSERT INTO appy_permissions (code, resource, action, description) VALUES
  ('employee:read', 'employee', 'read', 'View employee information'),
  ('employee:write', 'employee', 'write', 'Create or update employees'),
  ('employee:delete', 'employee', 'delete', 'Delete employees'),
  ('appraisal:read', 'appraisal', 'read', 'View appraisals'),
  ('appraisal:write', 'appraisal', 'write', 'Create or update appraisals'),
  ('appraisal:approve', 'appraisal', 'approve', 'Approve appraisals'),
  -- etc.
```

**appy_role_permissions** - Map roles to permissions
```sql
CREATE TABLE appy_role_permissions (
  role appy_user_role NOT NULL,
  permission_id UUID REFERENCES appy_permissions(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT now(),
  created_by UUID REFERENCES appy_users(id),
  PRIMARY KEY (role, permission_id)
);
```

**appy_user_permissions** - Direct user permission grants
```sql
CREATE TABLE appy_user_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES appy_users(id) ON DELETE CASCADE,
  permission_id UUID REFERENCES appy_permissions(id) ON DELETE CASCADE,
  granted_by UUID REFERENCES appy_users(id),
  reason TEXT,
  effective_from TIMESTAMPTZ DEFAULT now(),
  effective_to TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  CONSTRAINT valid_permission_dates CHECK (effective_to IS NULL OR effective_to > effective_from)
);
```

### Phase 2: Enhanced Permission Functions

#### 2.1 Core Permission Functions

```sql
-- Get all effective roles for a user
CREATE OR REPLACE FUNCTION get_user_effective_roles(p_user_id UUID)
RETURNS TABLE(role appy_user_role, department_id UUID, scope_type TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT 
    ur.role,
    ur.department_id,
    ur.scope_type
  FROM appy_user_roles ur
  WHERE ur.user_id = p_user_id
    AND ur.effective_from <= now()
    AND (ur.effective_to IS NULL OR ur.effective_to > now());
END;
$$;

-- Check if user has specific permission
CREATE OR REPLACE FUNCTION user_has_permission(
  p_user_id UUID,
  p_permission_code TEXT,
  p_resource_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_has_permission BOOLEAN := FALSE;
BEGIN
  -- Check super-admin first (bypass all checks)
  IF EXISTS (
    SELECT 1 FROM get_user_effective_roles(p_user_id)
    WHERE role = 'super-admin'
  ) THEN
    RETURN TRUE;
  END IF;

  -- Check role-based permissions
  SELECT EXISTS (
    SELECT 1
    FROM get_user_effective_roles(p_user_id) ur
    JOIN appy_role_permissions rp ON rp.role = ur.role
    JOIN appy_permissions p ON p.id = rp.permission_id
    WHERE p.code = p_permission_code
  ) INTO v_has_permission;

  IF v_has_permission THEN
    -- Additional resource-based checks can go here
    RETURN TRUE;
  END IF;

  -- Check direct user permissions
  SELECT EXISTS (
    SELECT 1
    FROM appy_user_permissions up
    JOIN appy_permissions p ON p.id = up.permission_id
    WHERE up.user_id = p_user_id
      AND p.code = p_permission_code
      AND up.effective_from <= now()
      AND (up.effective_to IS NULL OR up.effective_to > now())
  ) INTO v_has_permission;

  RETURN v_has_permission;
END;
$$;

-- Get current user ID from Clerk auth
CREATE OR REPLACE FUNCTION get_current_user_id()
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_clerk_id TEXT;
  v_user_id UUID;
BEGIN
  v_clerk_id := auth.uid()::text;
  
  SELECT id INTO v_user_id
  FROM appy_users
  WHERE clerk_id = v_clerk_id;
  
  RETURN v_user_id;
END;
$$;
```

#### 2.2 Department-Based Access Functions

```sql
-- Check if user can access employee in department hierarchy
CREATE OR REPLACE FUNCTION can_access_employee_in_department(
  p_user_id UUID,
  p_employee_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_dept UUID;
  v_employee_dept UUID;
  v_user_roles appy_user_role[];
BEGIN
  -- Get user's roles
  SELECT array_agg(role) INTO v_user_roles
  FROM get_user_effective_roles(p_user_id);

  -- Super-admin can access all
  IF 'super-admin' = ANY(v_user_roles) THEN
    RETURN TRUE;
  END IF;

  -- Get departments
  SELECT department_id INTO v_user_dept FROM appy_users WHERE id = p_user_id;
  SELECT department_id INTO v_employee_dept FROM appy_users WHERE id = p_employee_id;

  -- HR-admin can access their department
  IF 'hr-admin' = ANY(v_user_roles) AND v_user_dept = v_employee_dept THEN
    RETURN TRUE;
  END IF;

  -- Check manager hierarchy
  IF 'manager' = ANY(v_user_roles) OR 'senior-manager' = ANY(v_user_roles) THEN
    RETURN EXISTS (
      SELECT 1 
      FROM appy_user_reporting_hierarchy
      WHERE manager_id = p_user_id 
      AND employee_id = p_employee_id
    );
  END IF;

  RETURN FALSE;
END;
$$;
```

### Phase 3: Migration Strategy

#### 3.1 Data Migration Steps

1. **Create New Tables** (Week 1-2)
   - Deploy new table structures
   - Create indexes and constraints
   - Set up initial permission data

2. **Populate appy_users** (Week 3)
   ```sql
   -- Merge managers and employees into users
   INSERT INTO appy_users (
     clerk_id, email, full_name, first_name, last_name,
     department_id, compensation, compensation_rate, active
   )
   SELECT DISTINCT ON (COALESCE(m.user_id, e.clerk_user_id))
     COALESCE(m.user_id, e.clerk_user_id) as clerk_id,
     COALESCE(m.email, e.email) as email,
     COALESCE(m.full_name, e.full_name) as full_name,
     e.first_name,
     e.last_name,
     COALESCE(m.department_id, e.department_id) as department_id,
     e.compensation,
     e.rate as compensation_rate,
     COALESCE(m.active, e.active) as active
   FROM appy_managers m
   FULL OUTER JOIN appy_employees e 
     ON m.email = e.email OR m.user_id = e.clerk_user_id;
   ```

3. **Migrate Roles** (Week 4)
   ```sql
   -- Migrate primary roles
   INSERT INTO appy_user_roles (user_id, role, department_id, scope_type)
   SELECT 
     u.id,
     m.role,
     m.department_id,
     'department'
   FROM appy_managers m
   JOIN appy_users u ON u.clerk_id = m.user_id
   WHERE m.role IS NOT NULL;

   -- Migrate additional roles
   INSERT INTO appy_user_roles (user_id, role, scope_type)
   SELECT 
     u.id,
     jsonb_array_elements_text(m.additional_roles)::appy_user_role,
     'global'
   FROM appy_managers m
   JOIN appy_users u ON u.clerk_id = m.user_id
   WHERE m.additional_roles IS NOT NULL 
     AND jsonb_array_length(m.additional_roles) > 0;
   ```

4. **Update Foreign Keys** (Week 5-6)
   - Create mapping tables for ID translation
   - Update all foreign key references
   - Verify referential integrity

#### 3.2 Compatibility Layer

Create views for backward compatibility:

```sql
-- Managers view for legacy code
CREATE VIEW appy_managers_compat AS
SELECT 
  u.clerk_id as user_id,
  u.full_name,
  u.email,
  u.department_id,
  u.active,
  u.created_at,
  m.manager_id,
  r.role,
  (
    SELECT jsonb_agg(ur.role)
    FROM appy_user_roles ur
    WHERE ur.user_id = u.id
      AND ur.role != r.role
      AND ur.effective_to IS NULL
  ) as additional_roles
FROM appy_users u
LEFT JOIN LATERAL (
  SELECT role 
  FROM appy_user_roles 
  WHERE user_id = u.id 
    AND effective_to IS NULL 
  ORDER BY effective_from DESC 
  LIMIT 1
) r ON true
LEFT JOIN appy_user_reporting_hierarchy m ON m.employee_id = u.id;

-- Employees view for legacy code  
CREATE VIEW appy_employees_compat AS
SELECT 
  u.id,
  u.full_name,
  u.compensation,
  u.compensation_rate as rate,
  u.department_id,
  m.manager_id,
  u.active,
  u.created_at,
  u.first_name,
  u.last_name,
  u.email,
  u.metadata->>'bio' as bio,
  u.metadata->>'linkedin_url' as linkedin_url,
  u.metadata->>'twitter_url' as twitter_url,
  u.updated_at,
  u.metadata->>'role' as role,
  u.metadata->>'telegram_url' as telegram_url,
  u.clerk_id as clerk_user_id
FROM appy_users u
LEFT JOIN appy_user_reporting_hierarchy m ON m.employee_id = u.id;
```

### Phase 4: Enhanced Features

#### 4.1 Audit System

```sql
CREATE TABLE appy_permission_audit (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES appy_users(id),
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id UUID,
  changes JSONB,
  ip_address INET,
  user_agent TEXT,
  performed_by UUID REFERENCES appy_users(id),
  created_at TIMESTAMPTZ DEFAULT now()
);

CREATE INDEX idx_permission_audit_user ON appy_permission_audit(user_id);
CREATE INDEX idx_permission_audit_resource ON appy_permission_audit(resource_type, resource_id);
CREATE INDEX idx_permission_audit_created ON appy_permission_audit(created_at);
```

#### 4.2 Delegation System

```sql
CREATE TABLE appy_permission_delegations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  delegator_id UUID REFERENCES appy_users(id),
  delegate_id UUID REFERENCES appy_users(id),
  permission_id UUID REFERENCES appy_permissions(id),
  reason TEXT,
  effective_from TIMESTAMPTZ DEFAULT now(),
  effective_to TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  CONSTRAINT valid_delegation_period CHECK (effective_to > effective_from),
  CONSTRAINT no_self_delegation CHECK (delegator_id != delegate_id)
);
```

### Phase 5: RLS Policy Updates

#### 5.1 New RLS Policies

```sql
-- Enable RLS on new tables
ALTER TABLE appy_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_user_permissions ENABLE ROW LEVEL SECURITY;

-- Users can read their own data
CREATE POLICY "Users can read own data" ON appy_users
  FOR SELECT USING (id = get_current_user_id());

-- HR admins can read users in their department
CREATE POLICY "HR admins read department users" ON appy_users
  FOR SELECT USING (
    user_has_permission(get_current_user_id(), 'employee:read')
    AND can_access_employee_in_department(get_current_user_id(), id)
  );

-- Super admins can do everything
CREATE POLICY "Super admins full access" ON appy_users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM get_user_effective_roles(get_current_user_id())
      WHERE role = 'super-admin'
    )
  );
```

## Implementation Timeline

### Weeks 1-2: Planning & Setup
- Finalize design with stakeholders
- Set up test environment
- Create rollback procedures
- Write comprehensive tests

### Weeks 3-4: Core Tables
- Deploy new user tables
- Deploy permission tables
- Create core functions
- Initial data population

### Weeks 5-6: Migration Scripts
- Develop data migration scripts
- Test migrations on copy of production
- Create verification scripts
- Document edge cases

### Weeks 7-8: Application Updates
- Update authentication layer
- Modify API endpoints
- Update permission checks
- Create compatibility layer

### Weeks 9-10: Testing Phase
- Integration testing
- Performance testing
- Security audit
- User acceptance testing

### Weeks 11-12: Deployment
- Staged rollout
- Monitor for issues
- Performance optimization
- Documentation updates

## Risk Mitigation

### Technical Risks
1. **Data Loss**: Full backups before each migration step
2. **Downtime**: Use blue-green deployment
3. **Performance**: Extensive indexing and query optimization
4. **Compatibility**: Maintain views for legacy code

### Business Risks
1. **User Disruption**: Phased rollout with pilot groups
2. **Permission Gaps**: Extensive testing of all scenarios
3. **Training**: Comprehensive documentation and training

## Success Metrics

1. **Performance**
   - Query response time < 100ms for permission checks
   - No degradation in application performance

2. **Reliability**
   - Zero data loss during migration
   - 99.9% uptime maintained

3. **Security**
   - All permission checks properly enforced
   - Complete audit trail of all changes

4. **Usability**
   - No breaking changes for end users
   - Simplified permission management

## Conclusion

This comprehensive plan addresses all identified issues in the current permission system while providing a clear migration path. The new architecture will be more maintainable, scalable, and secure while maintaining backward compatibility during the transition period.