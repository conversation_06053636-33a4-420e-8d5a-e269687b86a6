// Browser Console Test Script for Joelle's HR Admin Access
// Copy and paste this into browser console while logged in as <PERSON>le

console.log('🧪 Testing Joelle HR Admin Access...');

async function testJoelleAccess() {
  try {
    // Test 1: Check authentication
    console.log('📋 Test 1: Checking authentication...');
    const authResponse = await fetch('/api/test-joelle-auth');
    const authData = await authResponse.json();
    
    console.log('✅ Authentication Test Results:', authData);
    
    if (!authData.isJoelle) {
      console.error('❌ Not logged in as <PERSON><PERSON>!');
      return;
    }
    
    if (!authData.clerkIdMatch) {
      console.warn('⚠️ Clerk ID mismatch detected - should be auto-fixed');
    }
    
    if (authData.currentUser?.role !== 'hr-admin') {
      console.error('❌ Role is not hr-admin:', authData.currentUser?.role);
    } else {
      console.log('✅ Correct hr-admin role detected');
    }

    // Test 2: Check add employee access
    console.log('📋 Test 2: Checking add employee access...');
    const accessResponse = await fetch('/api/test-add-employee-access');
    const accessData = await accessResponse.json();
    
    console.log('✅ Add Employee Access Results:', accessData);
    
    if (!accessData.access?.canAddEmployees) {
      console.error('❌ Cannot add employees!');
      console.log('Reason:', accessData.access);
    } else {
      console.log('✅ Can add employees');
    }

    // Test 3: Check if add employee page loads
    console.log('📋 Test 3: Testing add employee page navigation...');
    
    // Check if we can navigate to the page
    const currentUrl = window.location.href;
    const baseUrl = currentUrl.split('/dashboard')[0] || currentUrl.split('/api')[0];
    const addEmployeeUrl = `${baseUrl}/dashboard/add-employee`;
    
    console.log('🔗 Add Employee URL:', addEmployeeUrl);
    console.log('💡 Try navigating to this URL manually');

    // Summary
    console.log('\n📊 SUMMARY:');
    console.log('- Is Joelle:', authData.isJoelle);
    console.log('- Current Role:', authData.currentUser?.role);
    console.log('- Clerk ID Match:', authData.clerkIdMatch);
    console.log('- Can Add Employees:', accessData.access?.canAddEmployees);
    console.log('- Has Write Permission:', accessData.access?.hasWritePermission);
    
    if (authData.isJoelle && authData.currentUser?.role === 'hr-admin' && accessData.access?.canAddEmployees) {
      console.log('🎉 ALL TESTS PASSED! Joelle should be able to add employees.');
      console.log(`🔗 Navigate to: ${addEmployeeUrl}`);
    } else {
      console.log('❌ TESTS FAILED - Check the issues above');
    }

  } catch (error) {
    console.error('🚨 Test Error:', error);
  }
}

// Run the test
testJoelleAccess();
