#!/usr/bin/env tsx

/**
 * Force sync Jessica <PERSON>'s authentication data
 */

import { config } from 'dotenv'

// Load environment variables first
config({ path: '.env.local' })

// Verify environment variables are loaded
console.log('🔧 Environment check:')
console.log('   SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing')
console.log('   SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing')
console.log('   SUPABASE_SERVICE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Missing')
console.log('   CLERK_SECRET_KEY:', process.env.CLERK_SECRET_KEY ? '✅ Set' : '❌ Missing')

// Import after environment is loaded
import { forceSyncUser } from '../lib/auth'

async function main() {
  const email = '<EMAIL>'
  
  console.log('🔄 Starting force sync for <PERSON>...')
  console.log('📧 Email:', email)
  
  try {
    const result = await forceSyncUser(email)
    
    if (result.success) {
      console.log('✅ Sync successful!')
      console.log('📊 Result:', result.data)
    } else {
      console.error('❌ Sync failed!')
      console.error('🚨 Error:', result.error)
    }
  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

main().catch(console.error)
