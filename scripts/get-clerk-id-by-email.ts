#!/usr/bin/env bun

import 'dotenv/config'
import { clerkClient, type User } from '@clerk/clerk-sdk-node'
import { config } from 'dotenv'
import { resolve } from 'path'

// Load .env.local
config({ path: resolve(process.cwd(), '.env.local') })

interface ClerkUserResult {
  id: string
  email: string | undefined
  name: string
  createdAt: number
  lastSignInAt?: number | null
  emailVerified: boolean
}

interface BulkLookupResult {
  email: string
  clerkId: string | null
  name: string | null
  found: boolean
}

async function getClerkIdByEmail(email: string): Promise<ClerkUserResult | null> {
  try {
    console.log(`🔍 Searching for Clerk user with email: ${email}`)
    
    // Search for users by email
    const users = await clerkClient.users.getUserList({
      emailAddress: [email],
      limit: 10
    })

    if (users.data.length === 0) {
      console.log(`❌ No user found with email: ${email}`)
      return null
    }

    if (users.data.length > 1) {
      console.log(`⚠️  Multiple users found with email: ${email}`)
      users.data.forEach((user: User, index: number) => {
        console.log(`   ${index + 1}. ID: ${user.id}, Name: ${user.firstName} ${user.lastName}`)
      })
      return users.data.map((user: User) => ({
        id: user.id,
        email: user.emailAddresses[0]?.emailAddress,
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
        createdAt: user.createdAt,
        lastSignInAt: user.lastSignInAt,
        emailVerified: user.emailAddresses[0]?.verification?.status === 'verified'
      }))[0] // Return first match for consistency
    }

    const user = users.data[0]
    const result: ClerkUserResult = {
      id: user.id,
      email: user.emailAddresses[0]?.emailAddress,
      name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
      createdAt: user.createdAt,
      lastSignInAt: user.lastSignInAt,
      emailVerified: user.emailAddresses[0]?.verification?.status === 'verified'
    }

    console.log(`✅ Found user:`)
    console.log(`   Clerk ID: ${result.id}`)
    console.log(`   Name: ${result.name}`)
    console.log(`   Email: ${result.email}`)
    console.log(`   Email Verified: ${result.emailVerified}`)
    console.log(`   Created: ${new Date(result.createdAt).toLocaleString()}`)
    console.log(`   Last Sign In: ${result.lastSignInAt ? new Date(result.lastSignInAt).toLocaleString() : 'Never'}`)

    return result

  } catch (error: any) {
    console.error('❌ Error fetching user from Clerk:', error.message)
    
    if (error.status === 401) {
      console.error('🔑 Check your CLERK_SECRET_KEY environment variable')
    } else if (error.status === 422) {
      console.error('📧 Invalid email format provided')
    }
    
    return null
  }
}

// Bulk lookup function
async function bulkLookupClerkIds(emails: string[]): Promise<BulkLookupResult[]> {
  console.log(`🔍 Looking up ${emails.length} emails...`)
  const results: BulkLookupResult[] = []
  
  for (const email of emails) {
    console.log(`\n--- Processing: ${email} ---`)
    const result = await getClerkIdByEmail(email)
    results.push({
      email,
      clerkId: result?.id || null,
      name: result?.name || null,
      found: !!result
    })
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  console.log('\n📊 SUMMARY:')
  console.log(`Found: ${results.filter(r => r.found).length}/${results.length}`)
  console.log('\nResults:')
  results.forEach(r => {
    console.log(`${r.found ? '✅' : '❌'} ${r.email} -> ${r.clerkId || 'NOT FOUND'}`)
  })
  
  return results
}

// CLI usage
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    console.log('Usage: bun scripts/get-clerk-id-by-email.ts <email> [email2] [email3] ...')
    console.log('Example: bun scripts/get-clerk-id-by-email.ts <EMAIL>')
    process.exit(1)
  }

  if (args.length === 1) {
    // Single email lookup
    getClerkIdByEmail(args[0])
      .then(result => {
        if (result) {
          console.log(`\n📋 Copy this Clerk ID: ${result.id}`)
        }
        process.exit(result ? 0 : 1)
      })
      .catch(error => {
        console.error('Script failed:', error)
        process.exit(1)
      })
  } else {
    // Bulk lookup
    bulkLookupClerkIds(args)
      .then(results => {
        process.exit(0)
      })
      .catch(error => {
        console.error('Bulk lookup failed:', error)
        process.exit(1)
      })
  }
}

export { getClerkIdByEmail, bulkLookupClerkIds }