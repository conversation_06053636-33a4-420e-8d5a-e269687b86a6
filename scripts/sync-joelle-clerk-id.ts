// <PERSON>ript to sync <PERSON><PERSON>'s Clerk ID with the database
// Run with: npx tsx scripts/sync-joelle-clerk-id.ts

import { createClerkClient } from '@clerk/backend'
import { supabaseAdmin } from '../lib/supabase-admin'

// Initialize Clerk client with secret key
const clerkClient = createClerkClient({
  secretKey: process.env.CLERK_SECRET_KEY
})

async function syncJoelleClerkId() {
  console.log('🔄 Starting <PERSON>le Clerk ID sync...\n')

  try {
    // Step 1: Get <PERSON><PERSON>'s current database record
    console.log('📋 Step 1: Fetching <PERSON><PERSON>\'s database record...')
    const { data: joelleDb, error: dbError } = await supabaseAdmin
      .from('appy_managers')
      .select('*')
      .eq('email', '<EMAIL>')
      .single()

    if (dbError || !joelleDb) {
      console.error('❌ Could not find <PERSON><PERSON> in database:', dbError?.message)
      return
    }

    console.log('✅ Found <PERSON><PERSON> in database:', {
      current_clerk_id: joelleDb.user_id,
      full_name: joelleDb.full_name,
      email: joelleDb.email,
      role: joelleDb.role,
      active: joelleDb.active
    })

    // Step 2: Get Joelle's actual Clerk ID from Clerk
    console.log('\n📋 Step 2: Fetching Joelle\'s Clerk account...')
    
    // Search for Joelle by email in Clerk
    const clerkUsers = await clerkClient.users.getUserList({
      emailAddress: ['<EMAIL>']
    })

    if (!clerkUsers.data || clerkUsers.data.length === 0) {
      console.error('❌ Could not find Joelle in Clerk with email: <EMAIL>')
      console.log('💡 Make sure Joelle has signed up with this exact email address')
      return
    }

    if (clerkUsers.data.length > 1) {
      console.warn('⚠️ Multiple Clerk accounts found for this email')
    }

    const joelleClerk = clerkUsers.data[0]
    console.log('✅ Found Joelle in Clerk:', {
      clerk_id: joelleClerk.id,
      email: joelleClerk.emailAddresses[0]?.emailAddress,
      first_name: joelleClerk.firstName,
      last_name: joelleClerk.lastName,
      created_at: joelleClerk.createdAt,
      last_sign_in: joelleClerk.lastSignInAt
    })

    // Step 3: Compare and update if needed
    console.log('\n📋 Step 3: Comparing Clerk IDs...')
    
    if (joelleDb.user_id === joelleClerk.id) {
      console.log('✅ Clerk IDs already match! No update needed.')
      console.log(`Current ID: ${joelleDb.user_id}`)
      
      // Still verify the role and permissions
      if (joelleDb.role !== 'hr-admin') {
        console.log('🔧 Fixing role to hr-admin...')
        const { error: roleError } = await supabaseAdmin
          .from('appy_managers')
          .update({ role: 'hr-admin' })
          .eq('email', '<EMAIL>')
        
        if (roleError) {
          console.error('❌ Failed to update role:', roleError.message)
        } else {
          console.log('✅ Role updated to hr-admin')
        }
      }
      
      if (!joelleDb.active) {
        console.log('🔧 Activating account...')
        const { error: activeError } = await supabaseAdmin
          .from('appy_managers')
          .update({ active: true })
          .eq('email', '<EMAIL>')
        
        if (activeError) {
          console.error('❌ Failed to activate account:', activeError.message)
        } else {
          console.log('✅ Account activated')
        }
      }
      
    } else {
      console.log('🔧 Clerk ID mismatch detected, updating database...')
      console.log(`Old ID: ${joelleDb.user_id}`)
      console.log(`New ID: ${joelleClerk.id}`)
      
      // Update the database with the correct Clerk ID
      const { data: updateResult, error: updateError } = await supabaseAdmin
        .from('appy_managers')
        .update({ 
          user_id: joelleClerk.id,
          role: 'hr-admin', // Ensure role is correct
          active: true, // Ensure account is active
          full_name: `${joelleClerk.firstName} ${joelleClerk.lastName}`.trim() // Update name if needed
        })
        .eq('email', '<EMAIL>')
        .select()
        .single()

      if (updateError) {
        console.error('❌ Failed to update Clerk ID:', updateError.message)
        return
      }

      console.log('✅ Successfully updated Joelle\'s record:', updateResult)
    }

    // Step 4: Verify the fix
    console.log('\n📋 Step 4: Verifying the update...')
    const { data: verifyRecord, error: verifyError } = await supabaseAdmin
      .from('appy_managers')
      .select('*')
      .eq('email', '<EMAIL>')
      .single()

    if (verifyError || !verifyRecord) {
      console.error('❌ Verification failed:', verifyError?.message)
      return
    }

    console.log('✅ Final verification:', {
      clerk_id: verifyRecord.user_id,
      full_name: verifyRecord.full_name,
      email: verifyRecord.email,
      role: verifyRecord.role,
      active: verifyRecord.active,
      matches_clerk: verifyRecord.user_id === joelleClerk.id
    })

    console.log('\n🎉 Sync completed successfully!')
    console.log('\n📝 Next steps:')
    console.log('1. Have Joelle refresh her browser/clear cache')
    console.log('2. She should now see the "Add New Employee" button')
    console.log('3. She can access /dashboard/add-employee')
    console.log('4. Test by trying to add a new employee')

  } catch (error) {
    console.error('🚨 Sync failed:', error)
    if (error instanceof Error) {
      console.error('Error details:', error.message)
      console.error('Stack:', error.stack)
    }
  }
}

// Run the sync
syncJoelleClerkId()
