#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to fix <PERSON><PERSON><PERSON>'s Clerk ID
 * The issue: We kept the wrong record and deleted the one with the correct Clerk ID
 * Solution: Update the existing record with the correct Clerk ID and ensure accountant role
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌')
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅' : '❌')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

interface KhodrFix {
  currentUserId: string
  correctClerkId: string
  email: string
  fullName: string
  role: string
}

const khodrFix: KhodrFix = {
  currentUserId: 'user_wfnkv8znb_1754322738860', // Current wrong ID in database
  correctClerkId: 'user_30pNedmlrT0FmilnOOjprh04Ezt', // Correct Clerk ID from Clerk
  email: '<EMAIL>',
  fullName: 'Khodr Hassoun',
  role: 'accountant'
}

async function fixKhodrClerkId(): Promise<boolean> {
  console.log('🔄 Starting Khodr Hassoun Clerk ID fix...')
  console.log(`👤 Name: ${khodrFix.fullName}`)
  console.log(`📧 Email: ${khodrFix.email}`)
  console.log(`🆔 Current (wrong) ID: ${khodrFix.currentUserId}`)
  console.log(`🆔 Correct Clerk ID: ${khodrFix.correctClerkId}`)
  console.log(`👔 Role: ${khodrFix.role}`)

  try {
    // Step 1: Check if current record exists
    const { data: currentRecord, error: currentError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('user_id', khodrFix.currentUserId)
      .single()

    if (currentError) {
      console.error(`❌ Failed to find current record: ${currentError.message}`)
      return false
    }

    console.log(`📋 Found current record: ${currentRecord.full_name} (${currentRecord.role})`)

    // Step 2: Check if a record with the correct Clerk ID already exists
    const { data: existingClerkRecord, error: clerkError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('user_id', khodrFix.correctClerkId)
      .single()

    if (existingClerkRecord && !clerkError) {
      console.log(`⚠️  Record with correct Clerk ID already exists: ${existingClerkRecord.full_name}`)
      
      // Update the existing record to have correct name and role
      const { error: updateExistingError } = await supabase
        .from('appy_managers')
        .update({
          full_name: khodrFix.fullName,
          email: khodrFix.email,
          role: khodrFix.role,
          department_id: currentRecord.department_id,
          manager_id: currentRecord.manager_id,
          active: true
        })
        .eq('user_id', khodrFix.correctClerkId)

      if (updateExistingError) {
        console.error(`❌ Failed to update existing record: ${updateExistingError.message}`)
        return false
      }

      console.log(`✅ Updated existing record with correct details`)

      // Delete the old record with wrong ID
      const { error: deleteOldError } = await supabase
        .from('appy_managers')
        .delete()
        .eq('user_id', khodrFix.currentUserId)

      if (deleteOldError) {
        console.log(`⚠️  Warning: Failed to delete old record: ${deleteOldError.message}`)
      } else {
        console.log(`✅ Deleted old record with wrong ID`)
      }
    } else {
      // Step 3: Update current record with correct Clerk ID
      const { error: updateError } = await supabase
        .from('appy_managers')
        .update({
          user_id: khodrFix.correctClerkId,
          full_name: khodrFix.fullName,
          email: khodrFix.email,
          role: khodrFix.role
        })
        .eq('user_id', khodrFix.currentUserId)

      if (updateError) {
        console.error(`❌ Failed to update record: ${updateError.message}`)
        return false
      }

      console.log(`✅ Updated record with correct Clerk ID`)
    }

    // Step 4: Update employee-manager relationships
    const { error: emUpdateError } = await supabase
      .from('appy_employee_managers')
      .update({ manager_id: khodrFix.correctClerkId })
      .eq('manager_id', khodrFix.currentUserId)

    if (emUpdateError) {
      console.log(`⚠️  Warning: Failed to update employee-manager relationships: ${emUpdateError.message}`)
    } else {
      console.log(`✅ Updated employee-manager relationships`)
    }

    // Step 5: Update appraisal references
    const { error: appraisalUpdateError } = await supabase
      .from('appy_appraisals')
      .update({ manager_id: khodrFix.correctClerkId })
      .eq('manager_id', khodrFix.currentUserId)

    if (appraisalUpdateError) {
      console.log(`⚠️  Warning: Failed to update appraisal references: ${appraisalUpdateError.message}`)
    } else {
      console.log(`✅ Updated appraisal references`)
    }

    // Step 6: Update employee record if exists
    const { data: employeeRecord, error: empError } = await supabase
      .from('appy_employees')
      .select('*')
      .eq('email', khodrFix.email)
      .single()

    if (employeeRecord && !empError) {
      console.log(`📋 Found employee record: ${employeeRecord.full_name}`)
      
      const { error: empUpdateError } = await supabase
        .from('appy_employees')
        .update({
          clerk_user_id: khodrFix.correctClerkId,
          full_name: khodrFix.fullName
        })
        .eq('email', khodrFix.email)

      if (empUpdateError) {
        console.log(`⚠️  Warning: Failed to update employee record: ${empUpdateError.message}`)
      } else {
        console.log(`✅ Updated employee record with correct Clerk ID`)
      }
    }

    // Step 7: Verify the fix
    const { data: verifyRecord, error: verifyError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('user_id', khodrFix.correctClerkId)
      .single()

    if (verifyError) {
      console.error(`❌ Failed to verify fix: ${verifyError.message}`)
      return false
    }

    console.log(`\n🎉 Successfully fixed Khodr Hassoun's Clerk ID!`)
    console.log(`   Name: ${verifyRecord.full_name}`)
    console.log(`   Email: ${verifyRecord.email}`)
    console.log(`   Clerk ID: ${verifyRecord.user_id}`)
    console.log(`   Role: ${verifyRecord.role}`)

    return true

  } catch (error) {
    console.error('❌ Unexpected error:', error)
    return false
  }
}

// CLI usage
if (require.main === module) {
  console.log('🚀 Khodr Hassoun Clerk ID Fix Script')
  console.log('=' .repeat(50))
  
  fixKhodrClerkId()
    .then(success => {
      if (success) {
        console.log('\n✅ Fix completed successfully!')
      } else {
        console.log('\n❌ Fix failed!')
      }
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('💥 Script failed:', error)
      process.exit(1)
    })
}

export { fixKhodrClerkId }
