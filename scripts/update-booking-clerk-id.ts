#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> <NAME_EMAIL> Clerk ID in Supabase
 * This script will update the user_id for the booking user with the correct Clerk ID
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌')
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? '✅' : '❌')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

interface BookingUpdate {
  email: string
  clerkId: string
  fullName: string
}

const bookingUpdate: BookingUpdate = {
  email: '<EMAIL>',
  clerkId: 'user_30MYFpb8B6gR3oyzlc7QaZfdBn9',
  fullName: 'Roundtable Bookings'
}

async function updateBookingClerkId(): Promise<boolean> {
  console.log('🔄 Starting <EMAIL> Clerk ID update...')
  console.log(`📧 Email: ${bookingUpdate.email}`)
  console.log(`🆔 New Clerk ID: ${bookingUpdate.clerkId}`)
  console.log(`👤 Full Name: ${bookingUpdate.fullName}`)

  try {
    // Step 1: Check if user exists in managers table
    const { data: existingManager, error: fetchError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('email', bookingUpdate.email)
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error(`❌ Failed to fetch manager data: ${fetchError.message}`)
      return false
    }

    if (existingManager) {
      console.log(`📋 Found existing manager record: ${existingManager.full_name}`)
      
      // Update the existing record
      const { error: updateError } = await supabase
        .from('appy_managers')
        .update({
          user_id: bookingUpdate.clerkId,
          full_name: bookingUpdate.fullName
        })
        .eq('email', bookingUpdate.email)

      if (updateError) {
        console.error(`❌ Failed to update manager record: ${updateError.message}`)
        return false
      }

      console.log(`✅ Updated existing manager record with new Clerk ID`)
    } else {
      console.log(`📋 No existing manager found, creating new record...`)
      
      // Create new manager record
      const { error: insertError } = await supabase
        .from('appy_managers')
        .insert({
          user_id: bookingUpdate.clerkId,
          full_name: bookingUpdate.fullName,
          email: bookingUpdate.email,
          department_id: null, // Will be set later if needed
          active: true,
          manager_id: null,
          role: 'manager'
        })

      if (insertError) {
        console.error(`❌ Failed to create new manager record: ${insertError.message}`)
        return false
      }

      console.log(`✅ Created new manager record with Clerk ID`)
    }

    // Step 2: Check if user exists in employees table
    const { data: existingEmployee, error: empFetchError } = await supabase
      .from('appy_employees')
      .select('*')
      .eq('email', bookingUpdate.email)
      .single()

    if (empFetchError && empFetchError.code !== 'PGRST116') {
      console.error(`❌ Failed to fetch employee data: ${empFetchError.message}`)
      return false
    }

    if (existingEmployee) {
      console.log(`📋 Found existing employee record: ${existingEmployee.full_name}`)
      
      // Update the existing employee record
      const { error: empUpdateError } = await supabase
        .from('appy_employees')
        .update({
          user_id: bookingUpdate.clerkId,
          full_name: bookingUpdate.fullName
        })
        .eq('email', bookingUpdate.email)

      if (empUpdateError) {
        console.error(`❌ Failed to update employee record: ${empUpdateError.message}`)
        return false
      }

      console.log(`✅ Updated existing employee record with new Clerk ID`)
    }

    // Step 3: Update any appraisal references (if any exist)
    const { error: appraisalUpdateError } = await supabase
      .from('appy_appraisals')
      .update({ manager_id: bookingUpdate.clerkId })
      .eq('manager_id', existingManager?.user_id || 'temp_booking_id')

    if (appraisalUpdateError) {
      console.log(`⚠️  Warning: Failed to update appraisal references: ${appraisalUpdateError.message}`)
    } else {
      console.log(`✅ Updated appraisal manager references`)
    }

    console.log(`\n🎉 <NAME_EMAIL> Clerk ID!`)
    console.log(`   Email: ${bookingUpdate.email}`)
    console.log(`   Clerk ID: ${bookingUpdate.clerkId}`)
    console.log(`   Name: ${bookingUpdate.fullName}`)

    return true

  } catch (error) {
    console.error('❌ Unexpected error:', error)
    return false
  }
}

// CLI usage
if (require.main === module) {
  console.log('🚀 <EMAIL> Clerk ID Update Script')
  console.log('=' .repeat(50))
  
  updateBookingClerkId()
    .then(success => {
      if (success) {
        console.log('\n✅ Update completed successfully!')
      } else {
        console.log('\n❌ Update failed!')
      }
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('💥 Script failed:', error)
      process.exit(1)
    })
}

export { updateBookingClerkId }
