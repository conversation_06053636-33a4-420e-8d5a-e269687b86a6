#!/usr/bin/env node

const { clerkClient } = require('@clerk/clerk-sdk-node')

// Manager emails to check
const managerEmails = [
  '<EMAIL>',
  '<EMAIL>', 
  'hannahhugh<PERSON><EMAIL>',
  '<EMAIL>'
]

async function getClerkIdByEmail(email) {
  try {
    console.log(`🔍 Searching for Clerk user with email: ${email}`)
    
    const users = await clerkClient.users.getUserList({
      emailAddress: [email],
      limit: 10
    })

    if (users.data.length === 0) {
      console.log(`❌ No user found with email: ${email}`)
      return null
    }

    const user = users.data[0]
    const result = {
      id: user.id,
      email: user.emailAddresses[0]?.emailAddress,
      name: `${user.firstName} ${user.lastName}`.trim(),
      createdAt: user.createdAt,
      lastSignInAt: user.lastSignInAt
    }

    console.log(`✅ Found user:`)
    console.log(`   Clerk ID: ${result.id}`)
    console.log(`   Name: ${result.name}`)
    console.log(`   Email: ${result.email}`)
    console.log(`   Last Sign In: ${result.lastSignInAt ? new Date(result.lastSignInAt).toLocaleString() : 'Never'}`)

    return result

  } catch (error) {
    console.error(`❌ Error fetching user from Clerk:`, error.message)
    return null
  }
}

async function checkAllManagerEmails() {
  console.log('🔍 Checking Clerk IDs for manager emails...')
  console.log('=' .repeat(50))
  
  const results = []
  
  for (const email of managerEmails) {
    console.log(`\n--- Processing: ${email} ---`)
    const result = await getClerkIdByEmail(email)
    
    results.push({
      email,
      clerkId: result?.id || null,
      name: result?.name || null,
      found: !!result,
      lastSignIn: result?.lastSignInAt || null
    })
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  console.log('\n📊 SUMMARY:')
  console.log(`Found: ${results.filter(r => r.found).length}/${results.length}`)
  console.log('\n📋 Results:')
  results.forEach(r => {
    const status = r.found ? '✅' : '❌'
    const signInInfo = r.lastSignIn ? `(Last sign in: ${new Date(r.lastSignIn).toLocaleString()})` : '(Never signed in)'
    console.log(`${status} ${r.email}`)
    if (r.found) {
      console.log(`   Clerk ID: ${r.clerkId}`)
      console.log(`   Name: ${r.name}`)
      console.log(`   ${signInInfo}`)
    }
  })
  
  console.log('\n🔧 Next steps for found users:')
  results.filter(r => r.found).forEach(r => {
    console.log(`node scripts/update-manager-clerk-ids.js ${r.email} ${r.clerkId}`)
  })
  
  return results
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    checkAllManagerEmails()
      .catch(error => {
        console.error('Script failed:', error)
        process.exit(1)
      })
  } else if (args.length === 1) {
    getClerkIdByEmail(args[0])
      .then(result => {
        if (result) {
          console.log(`\n📋 Copy this command:`)
          console.log(`node scripts/update-manager-clerk-ids.js ${result.email} ${result.id}`)
        }
        process.exit(result ? 0 : 1)
      })
      .catch(error => {
        console.error('Script failed:', error)
        process.exit(1)
      })
  } else {
    console.log('Usage: node scripts/get-clerk-ids-for-managers.js [email]')
    console.log('   No args: Check all manager emails')
    console.log('   With email: Check specific email')
    process.exit(1)
  }
}

module.exports = { getClerkIdByEmail, checkAllManagerEmails }
