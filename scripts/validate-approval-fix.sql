-- Validation script to test the approval ID fix
-- This script verifies that the enhanced function works correctly

-- Test 1: Check if <PERSON> can see her pending approval with her email
SELECT 
  'Test 1: <PERSON>ail Lookup' as test_name,
  COUNT(*) as pending_approvals_found
FROM get_pending_approvals_for_user(
  'non_existent_clerk_id', 
  '<EMAIL>'
);

-- Test 2: Check if the direct ID lookup still works
SELECT 
  'Test 2: Direct ID Lookup' as test_name,
  COUNT(*) as pending_approvals_found
FROM get_pending_approvals_for_user(
  'user_2zgIPJfN1J3mcwjHH27wFQT9NQW', 
  NULL
);

-- Test 3: Check if both ID and email parameters work together
SELECT 
  'Test 3: Both ID and Email' as test_name,
  COUNT(*) as pending_approvals_found
FROM get_pending_approvals_for_user(
  'user_2zgIPJfN1J3mcwjHH27wFQT9NQW', 
  '<EMAIL>'
);

-- Test 4: Verify manager hierarchy is intact
SELECT 
  'Test 4: Manager Hierarchy' as test_name,
  m1.full_name as manager,
  m1.role,
  m2.full_name as reports_to,
  CASE 
    WHEN m1.manager_id = m2.user_id THEN 'Valid'
    ELSE 'Invalid'
  END as hierarchy_status
FROM appy_managers m1
LEFT JOIN appy_managers m2 ON m1.manager_id = m2.user_id
WHERE m1.user_id = 'user_30BwNFuU7WcoWLq4Ha4TEGfa39h' -- Mia
   OR m1.user_id = 'user_2zgIPJfN1J3mcwjHH27wFQT9NQW'; -- Mona

-- Test 5: Check active approval workflows
SELECT 
  'Test 5: Active Workflows' as test_name,
  COUNT(*) as active_workflows,
  COUNT(DISTINCT s.approver_id) as unique_approvers
FROM appy_approval_workflows w
JOIN appy_approval_steps s ON w.id = s.workflow_id
WHERE w.status IN ('pending', 'in_progress')
  AND s.status = 'pending';

-- Test 6: Verify specific pending approval details
SELECT 
  'Test 6: Specific Approval Details' as test_name,
  a.id as appraisal_id,
  e.full_name as employee_name,
  submitter.full_name as submitted_by,
  approver.full_name as pending_approver,
  s.status as step_status,
  w.status as workflow_status
FROM appy_appraisals a
JOIN appy_employees e ON a.employee_id = e.id
JOIN appy_managers submitter ON a.manager_id = submitter.user_id
JOIN appy_approval_workflows w ON a.id = w.appraisal_id
JOIN appy_approval_steps s ON w.id = s.workflow_id
JOIN appy_managers approver ON s.approver_id = approver.user_id
WHERE a.id = '3c7d8a10-88f4-47a7-b838-b5d788645281'; -- Mia's appraisal for Mariana