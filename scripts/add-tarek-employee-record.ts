#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to add <PERSON><PERSON> as an employee record
 * He exists as a senior-manager but is missing from appy_employees table
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const tarekData = {
  clerkId: 'user_30YLxMbbJa4ZIuLhgHV3Hectxlw',
  fullName: 'Tarek Hassoun',
  email: '<EMAIL>',
  departmentId: '9f1d9d21-7d46-4a28-bc33-b164cee67b68', // Same as his manager record
  managerId: 'user_2zgBSj0wjNqywixy4Btq5nsu5QU' // Bob Wazneh (his manager from manager record)
}

async function addTarekEmployeeRecord(): Promise<boolean> {
  console.log('🔄 Adding Tarek Hassoun employee record...')
  console.log(`👤 Name: ${tarekData.fullName}`)
  console.log(`📧 Email: ${tarekData.email}`)
  console.log(`🆔 Clerk ID: ${tarekData.clerkId}`)
  console.log(`🏢 Department ID: ${tarekData.departmentId}`)
  console.log(`👨‍💼 Manager ID: ${tarekData.managerId}`)

  try {
    // Step 1: Verify manager record exists
    const { data: managerRecord, error: managerError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('user_id', tarekData.clerkId)
      .single()

    if (managerError || !managerRecord) {
      console.error(`❌ Manager record not found: ${managerError?.message}`)
      return false
    }

    console.log(`📋 Verified manager record: ${managerRecord.full_name} (${managerRecord.role})`)

    // Step 2: Double-check employee record doesn't exist
    const { data: existingEmployee, error: checkError } = await supabase
      .from('appy_employees')
      .select('*')
      .eq('email', tarekData.email)
      .single()

    if (existingEmployee && !checkError) {
      console.log(`⚠️  Employee record already exists: ${existingEmployee.full_name}`)
      return false
    }

    // Step 3: Get department name for logging
    const { data: department, error: deptError } = await supabase
      .from('appy_departments')
      .select('name')
      .eq('id', tarekData.departmentId)
      .single()

    const departmentName = department?.name || 'Unknown Department'
    console.log(`🏢 Department: ${departmentName}`)

    // Step 4: Create employee record
    const { data: newEmployee, error: insertError } = await supabase
      .from('appy_employees')
      .insert({
        full_name: tarekData.fullName,
        first_name: 'Tarek',
        last_name: 'Hassoun',
        email: tarekData.email,
        clerk_user_id: tarekData.clerkId,
        role: 'Senior Manager', // Job title
        role_rank: 'employee', // System role rank
        rate: 'monthly',
        department_id: tarekData.departmentId,
        manager_id: tarekData.managerId,
        active: true,
        compensation: 0 // Placeholder value
      })
      .select()
      .single()

    if (insertError) {
      console.error(`❌ Failed to create employee record: ${insertError.message}`)
      return false
    }

    console.log(`✅ Created employee record: ${newEmployee.id}`)

    // Step 5: Create employee-manager relationship
    const { error: relationError } = await supabase
      .from('appy_employee_managers')
      .insert({
        employee_id: newEmployee.id,
        manager_id: tarekData.managerId,
        is_primary: true,
        assigned_at: new Date().toISOString()
      })

    if (relationError) {
      console.log(`⚠️  Warning: Failed to create employee-manager relationship: ${relationError.message}`)
    } else {
      console.log(`✅ Created employee-manager relationship`)
    }

    // Step 6: Verify the creation
    const { data: verifyEmployee, error: verifyError } = await supabase
      .from('appy_employees')
      .select(`
        id,
        full_name,
        email,
        clerk_user_id,
        role,
        role_rank,
        department_id,
        manager_id,
        active,
        appy_departments:department_id (name)
      `)
      .eq('id', newEmployee.id)
      .single()

    if (verifyError) {
      console.error(`❌ Failed to verify creation: ${verifyError.message}`)
      return false
    }

    console.log(`\n🎉 Successfully added Tarek Hassoun as employee!`)
    console.log(`   Employee ID: ${verifyEmployee.id}`)
    console.log(`   Name: ${verifyEmployee.full_name}`)
    console.log(`   Email: ${verifyEmployee.email}`)
    console.log(`   Clerk ID: ${verifyEmployee.clerk_user_id}`)
    console.log(`   Job Title: ${verifyEmployee.role}`)
    console.log(`   Role Rank: ${verifyEmployee.role_rank}`)
    console.log(`   Department: ${(verifyEmployee as any).appy_departments?.name}`)
    console.log(`   Manager ID: ${verifyEmployee.manager_id}`)
    console.log(`   Active: ${verifyEmployee.active}`)

    return true

  } catch (error) {
    console.error('❌ Unexpected error:', error)
    return false
  }
}

// CLI usage
if (require.main === module) {
  console.log('🚀 Add Tarek Hassoun Employee Record Script')
  console.log('=' .repeat(50))
  
  addTarekEmployeeRecord()
    .then(success => {
      if (success) {
        console.log('\n✅ Addition completed successfully!')
      } else {
        console.log('\n❌ Addition failed!')
      }
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('💥 Script failed:', error)
      process.exit(1)
    })
}

export { addTarekEmployeeRecord }
