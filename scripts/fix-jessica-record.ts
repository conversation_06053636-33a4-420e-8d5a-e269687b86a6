#!/usr/bin/env tsx

/**
 * <PERSON><PERSON>t to fix <PERSON>'s record in Supabase
 * Issues to fix:
 * 1. Placeholder User ID: jessica_manager_id -> proper Clerk ID
 * 2. Email: Ensure it's <EMAIL> (no extra spaces)
 * 3. Full name: Ensure it's "<PERSON>"
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  console.error('   NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅' : '❌')
  console.error('   SUPABASE_SERVICE_ROLE_KEY:', supabaseService<PERSON><PERSON> ? '✅' : '❌')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

interface JessicaUpdate {
  oldUserId: string
  newUserId: string
  email: string
  fullName: string
}

const jessicaUpdate: JessicaUpdate = {
  oldUserId: 'jessica_manager_id',
  newUserId: 'user_30MYFpb8B6gR3oyzlc7QaZfdBn9', // Same Clerk <NAME_EMAIL>
  email: '<EMAIL>',
  fullName: 'Jessica Hasson'
}

async function fixJessicaRecord(): Promise<boolean> {
  console.log('🔄 Starting Jessica Hasson record fix...')
  console.log(`👤 Name: ${jessicaUpdate.fullName}`)
  console.log(`📧 Email: ${jessicaUpdate.email}`)
  console.log(`🆔 Old User ID: ${jessicaUpdate.oldUserId}`)
  console.log(`🆔 New Clerk ID: ${jessicaUpdate.newUserId}`)

  try {
    // Step 1: Check current Jessica record
    const { data: currentJessica, error: fetchError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('user_id', jessicaUpdate.oldUserId)
      .single()

    if (fetchError) {
      console.error(`❌ Failed to fetch Jessica's current record: ${fetchError.message}`)
      
      // Try to find by name if user_id lookup fails
      const { data: jessicaByName, error: nameError } = await supabase
        .from('appy_managers')
        .select('*')
        .ilike('full_name', '%jessica%')
        .single()

      if (nameError) {
        console.error(`❌ Failed to find Jessica by name either: ${nameError.message}`)
        return false
      }
      
      console.log(`📋 Found Jessica by name instead: ${jessicaByName.full_name}`)
      console.log(`   Current user_id: ${jessicaByName.user_id}`)
      console.log(`   Current email: ${jessicaByName.email}`)
      
      // Update the old user ID for the fix
      jessicaUpdate.oldUserId = jessicaByName.user_id
    } else {
      console.log(`📋 Found Jessica's current record: ${currentJessica.full_name}`)
      console.log(`   Current user_id: ${currentJessica.user_id}`)
      console.log(`   Current email: ${currentJessica.email}`)
    }

    // Step 2: Check if there's already a record with the new Clerk ID
    const { data: existingClerkRecord, error: clerkCheckError } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('user_id', jessicaUpdate.newUserId)
      .single()

    if (existingClerkRecord && !clerkCheckError) {
      console.log(`⚠️  Record with Clerk ID ${jessicaUpdate.newUserId} already exists:`)
      console.log(`   Name: ${existingClerkRecord.full_name}`)
      console.log(`   Email: ${existingClerkRecord.email}`)
      
      if (existingClerkRecord.full_name.toLowerCase().includes('jessica')) {
        console.log(`✅ This appears to be Jessica's record, updating it...`)
        
        // Update the existing record
        const { error: updateError } = await supabase
          .from('appy_managers')
          .update({
            full_name: jessicaUpdate.fullName,
            email: jessicaUpdate.email
          })
          .eq('user_id', jessicaUpdate.newUserId)

        if (updateError) {
          console.error(`❌ Failed to update existing record: ${updateError.message}`)
          return false
        }

        console.log(`✅ Updated existing Jessica record with correct name and email`)
        
        // Delete the old placeholder record if it exists and is different
        if (jessicaUpdate.oldUserId !== jessicaUpdate.newUserId) {
          const { error: deleteError } = await supabase
            .from('appy_managers')
            .delete()
            .eq('user_id', jessicaUpdate.oldUserId)

          if (deleteError) {
            console.log(`⚠️  Warning: Failed to delete old placeholder record: ${deleteError.message}`)
          } else {
            console.log(`✅ Deleted old placeholder record`)
          }
        }
      } else {
        console.log(`🔄 Merging records: Jessica Hasson and ${existingClerkRecord.full_name} both use ${jessicaUpdate.email}`)

        // Since both Jessica and Roundtable Bookings use the same email,
        // we'll update the existing Clerk record to be Jessica's record
        const { error: mergeError } = await supabase
          .from('appy_managers')
          .update({
            full_name: jessicaUpdate.fullName, // Change to Jessica Hasson
            email: jessicaUpdate.email
          })
          .eq('user_id', jessicaUpdate.newUserId)

        if (mergeError) {
          console.error(`❌ Failed to merge records: ${mergeError.message}`)
          return false
        }

        console.log(`✅ Merged records - updated to Jessica Hasson`)

        // Delete the old placeholder record
        const { error: deleteError } = await supabase
          .from('appy_managers')
          .delete()
          .eq('user_id', jessicaUpdate.oldUserId)

        if (deleteError) {
          console.log(`⚠️  Warning: Failed to delete old placeholder record: ${deleteError.message}`)
        } else {
          console.log(`✅ Deleted old placeholder record`)
        }
      }
    } else {
      // Step 3: Update Jessica's record with correct Clerk ID, name, and email
      const { error: updateError } = await supabase
        .from('appy_managers')
        .update({
          user_id: jessicaUpdate.newUserId,
          full_name: jessicaUpdate.fullName,
          email: jessicaUpdate.email
        })
        .eq('user_id', jessicaUpdate.oldUserId)

      if (updateError) {
        console.error(`❌ Failed to update Jessica's record: ${updateError.message}`)
        return false
      }

      console.log(`✅ Updated Jessica's manager record with correct Clerk ID, name, and email`)
    }

    // Step 4: Update employee-manager relationships
    const { error: emUpdateError } = await supabase
      .from('appy_employee_managers')
      .update({ manager_id: jessicaUpdate.newUserId })
      .eq('manager_id', jessicaUpdate.oldUserId)

    if (emUpdateError) {
      console.log(`⚠️  Warning: Failed to update employee-manager relationships: ${emUpdateError.message}`)
    } else {
      console.log(`✅ Updated employee-manager relationships`)
    }

    // Step 5: Update appraisal manager references
    const { error: appraisalUpdateError } = await supabase
      .from('appy_appraisals')
      .update({ manager_id: jessicaUpdate.newUserId })
      .eq('manager_id', jessicaUpdate.oldUserId)

    if (appraisalUpdateError) {
      console.log(`⚠️  Warning: Failed to update appraisal references: ${appraisalUpdateError.message}`)
    } else {
      console.log(`✅ Updated appraisal manager references`)
    }

    // Step 6: Check if Jessica exists in employees table and update if needed
    const { data: jessicaEmployee, error: empError } = await supabase
      .from('appy_employees')
      .select('*')
      .eq('email', jessicaUpdate.email)
      .single()

    if (jessicaEmployee && !empError) {
      console.log(`📋 Found Jessica in employees table: ${jessicaEmployee.full_name}`)
      
      // Update employee record with correct user_id if needed
      if (jessicaEmployee.user_id !== jessicaUpdate.newUserId) {
        const { error: empUpdateError } = await supabase
          .from('appy_employees')
          .update({
            user_id: jessicaUpdate.newUserId,
            full_name: jessicaUpdate.fullName
          })
          .eq('email', jessicaUpdate.email)

        if (empUpdateError) {
          console.log(`⚠️  Warning: Failed to update employee record: ${empUpdateError.message}`)
        } else {
          console.log(`✅ Updated Jessica's employee record`)
        }
      }
    }

    console.log(`\n🎉 Successfully fixed Jessica Hasson's record!`)
    console.log(`   Name: ${jessicaUpdate.fullName}`)
    console.log(`   Email: ${jessicaUpdate.email}`)
    console.log(`   Clerk ID: ${jessicaUpdate.newUserId}`)

    return true

  } catch (error) {
    console.error('❌ Unexpected error:', error)
    return false
  }
}

// CLI usage
if (require.main === module) {
  console.log('🚀 Jessica Hasson Record Fix Script')
  console.log('=' .repeat(50))
  
  fixJessicaRecord()
    .then(success => {
      if (success) {
        console.log('\n✅ Fix completed successfully!')
      } else {
        console.log('\n❌ Fix failed!')
      }
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('💥 Script failed:', error)
      process.exit(1)
    })
}

export { fixJessicaRecord }
