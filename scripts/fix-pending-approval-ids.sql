-- Migration script to fix pending approval step IDs
-- This script updates approval steps to use current Clerk IDs when they exist

-- Step 1: Log current state for audit trail
INSERT INTO appy_approval_history (
  workflow_id,
  action,
  actor_id,
  actor_name,
  new_status,
  comments,
  metadata
)
SELECT 
  s.workflow_id,
  'id_migration_start',
  'system',
  'Migration Script',
  'pending',
  'Starting ID migration for pending approval steps',
  jsonb_build_object(
    'old_approver_id', s.approver_id,
    'approver_email', m.email,
    'migration_timestamp', NOW()
  )
FROM appy_approval_steps s
JOIN appy_managers m ON s.approver_id = m.user_id
WHERE s.status = 'pending'
  AND EXISTS (
    SELECT 1 FROM appy_approval_workflows w 
    WHERE w.id = s.workflow_id 
    AND w.status IN ('pending', 'in_progress')
  );

-- Step 2: Update approval steps where we have email-based matches
-- This handles cases where the approver's user_id might have changed
UPDATE appy_approval_steps 
SET approver_id = current_manager.user_id
FROM (
  -- Find the current active manager record for each email
  SELECT DISTINCT ON (email) 
    user_id, 
    email, 
    full_name,
    active
  FROM appy_managers 
  WHERE active = true
  ORDER BY email, created_at DESC
) current_manager
JOIN appy_managers old_manager ON current_manager.email = old_manager.email
WHERE appy_approval_steps.approver_id = old_manager.user_id
  AND appy_approval_steps.status = 'pending'
  AND current_manager.user_id != old_manager.user_id
  AND EXISTS (
    SELECT 1 FROM appy_approval_workflows w 
    WHERE w.id = appy_approval_steps.workflow_id 
    AND w.status IN ('pending', 'in_progress')
  );

-- Step 3: Log completion for audit trail
INSERT INTO appy_approval_history (
  workflow_id,
  action,
  actor_id,
  actor_name,
  new_status,
  comments,
  metadata
)
SELECT DISTINCT
  s.workflow_id,
  'id_migration_complete',
  'system',
  'Migration Script',
  'pending',
  'Completed ID migration for pending approval steps',
  jsonb_build_object(
    'updated_approver_id', s.approver_id,
    'approver_name', m.full_name,
    'migration_timestamp', NOW()
  )
FROM appy_approval_steps s
JOIN appy_managers m ON s.approver_id = m.user_id
WHERE s.status = 'pending'
  AND EXISTS (
    SELECT 1 FROM appy_approval_workflows w 
    WHERE w.id = s.workflow_id 
    AND w.status IN ('pending', 'in_progress')
  );

-- Step 4: Report summary
SELECT 
  'Migration Summary' as report_type,
  COUNT(*) as total_pending_steps,
  COUNT(DISTINCT approver_id) as unique_approvers,
  COUNT(DISTINCT workflow_id) as affected_workflows
FROM appy_approval_steps s
WHERE s.status = 'pending'
  AND EXISTS (
    SELECT 1 FROM appy_approval_workflows w 
    WHERE w.id = s.workflow_id 
    AND w.status IN ('pending', 'in_progress')
  );