#!/usr/bin/env node

/**
 * <PERSON>ript to lookup Clerk IDs for emails from a CSV file
 * Usage: node scripts/csv-clerk-lookup.js <csv-file>
 * Example: node scripts/csv-clerk-lookup.js docs/contractors/Performance\ Appraisal\ -\ Emails.csv
 */

const fs = require('fs')
const path = require('path')
const { clerkClient } = require('@clerk/clerk-sdk-node')

function parseCSV(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf-8')
    const lines = content.split('\n').filter(line => line.trim())
    
    if (lines.length === 0) {
      throw new Error('CSV file is empty')
    }
    
    // Skip header row and parse data
    const dataLines = lines.slice(1)
    const emails = []
    
    dataLines.forEach((line, index) => {
      const [name, email] = line.split(',').map(col => col.trim().replace(/"/g, ''))
      if (email && email.includes('@')) {
        emails.push({ name, email, lineNumber: index + 2 })
      }
    })
    
    console.log(`📄 Parsed ${emails.length} valid emails from CSV`)
    return emails
    
  } catch (error) {
    console.error(`❌ Error reading CSV file: ${error.message}`)
    return []
  }
}

async function lookupClerkIdForEmail(email) {
  try {
    const users = await clerkClient.users.getUserList({
      emailAddress: [email],
      limit: 1
    })
    
    if (users.data.length > 0) {
      const user = users.data[0]
      return {
        found: true,
        clerkId: user.id,
        clerkName: `${user.firstName} ${user.lastName}`.trim(),
        emailVerified: user.emailAddresses[0]?.verification?.status === 'verified'
      }
    }
    
    return { found: false, clerkId: null, clerkName: null, emailVerified: false }
    
  } catch (error) {
    console.error(`Error looking up ${email}:`, error.message)
    return { found: false, clerkId: null, clerkName: null, emailVerified: false, error: error.message }
  }
}

async function processCSVLookup(csvFilePath) {
  console.log(`🔍 Processing CSV file: ${csvFilePath}`)
  
  const emailData = parseCSV(csvFilePath)
  if (emailData.length === 0) {
    console.log('❌ No valid emails found in CSV')
    return
  }
  
  const results = []
  let found = 0
  let notFound = 0
  
  console.log(`\n🔄 Looking up ${emailData.length} emails...`)
  
  for (const { name, email, lineNumber } of emailData) {
    process.stdout.write(`Processing ${email}... `)
    
    const lookup = await lookupClerkIdForEmail(email)
    
    if (lookup.found) {
      console.log(`✅ Found: ${lookup.clerkId}`)
      found++
    } else {
      console.log(`❌ Not found`)
      notFound++
    }
    
    results.push({
      csvName: name,
      email,
      lineNumber,
      ...lookup
    })
    
    // Small delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 150))
  }
  
  // Generate output files
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
  const outputDir = 'scripts/output'
  
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }
  
  // Write detailed results to JSON
  const jsonOutput = path.join(outputDir, `clerk-lookup-${timestamp}.json`)
  fs.writeFileSync(jsonOutput, JSON.stringify(results, null, 2))
  
  // Write CSV with results
  const csvOutput = path.join(outputDir, `clerk-lookup-${timestamp}.csv`)
  const csvHeader = 'Name,Email,ClerkID,ClerkName,EmailVerified,Found,Error\n'
  const csvRows = results.map(r => 
    `"${r.csvName}","${r.email}","${r.clerkId || ''}","${r.clerkName || ''}","${r.emailVerified}","${r.found}","${r.error || ''}"`
  ).join('\n')
  fs.writeFileSync(csvOutput, csvHeader + csvRows)
  
  // Write SQL update statements for found users
  const sqlOutput = path.join(outputDir, `update-clerk-ids-${timestamp}.sql`)
  const foundUsers = results.filter(r => r.found)
  const sqlStatements = foundUsers.map(r => 
    `UPDATE appy_managers SET user_id = '${r.clerkId}' WHERE email = '${r.email}';`
  ).join('\n')
  
  if (foundUsers.length > 0) {
    fs.writeFileSync(sqlOutput, `-- SQL statements to update Clerk IDs\n-- Generated: ${new Date().toISOString()}\n\n${sqlStatements}`)
  }
  
  // Summary
  console.log(`\n📊 SUMMARY:`)
  console.log(`✅ Found: ${found}`)
  console.log(`❌ Not Found: ${notFound}`)
  console.log(`📁 Results saved to:`)
  console.log(`   JSON: ${jsonOutput}`)
  console.log(`   CSV: ${csvOutput}`)
  if (foundUsers.length > 0) {
    console.log(`   SQL: ${sqlOutput}`)
  }
  
  // Show not found emails
  const notFoundEmails = results.filter(r => !r.found)
  if (notFoundEmails.length > 0) {
    console.log(`\n❌ Emails not found in Clerk:`)
    notFoundEmails.forEach(r => {
      console.log(`   ${r.email} (${r.csvName})`)
    })
  }
  
  return results
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2)
  
  if (args.length !== 1) {
    console.log('Usage: node scripts/csv-clerk-lookup.js <csv-file>')
    console.log('Example: node scripts/csv-clerk-lookup.js "docs/contractors/Performance Appraisal - Emails.csv"')
    process.exit(1)
  }
  
  const csvFile = args[0]
  
  if (!fs.existsSync(csvFile)) {
    console.error(`❌ CSV file not found: ${csvFile}`)
    process.exit(1)
  }
  
  processCSVLookup(csvFile)
    .then(() => {
      console.log('\n✅ CSV processing complete!')
      process.exit(0)
    })
    .catch(error => {
      console.error('❌ Script failed:', error)
      process.exit(1)
    })
}

module.exports = { processCSVLookup, lookupClerkIdForEmail }
