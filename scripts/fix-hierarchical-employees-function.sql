-- Production-ready fix for get_hierarchical_employees function type mismatch
-- This script resolves the PostgreSQL error: "Returned type character varying(100) does not match expected type appy_user_role"

-- Step 1: Backup current function definition
DO $$
BEGIN
    -- Log the fix start
    RAISE NOTICE 'Starting get_hierarchical_employees function fix at %', NOW();
    
    -- Check if function exists
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_hierarchical_employees') THEN
        RAISE NOTICE 'Function get_hierarchical_employees exists, proceeding with fix';
    ELSE
        RAISE NOTICE 'Function get_hierarchical_employees does not exist, will create new';
    END IF;
END $$;

-- Step 2: Drop existing function if it exists
DROP FUNCTION IF EXISTS get_hierarchical_employees(text);

-- Step 3: Create the corrected function with proper type handling
CREATE OR REPLACE FUNCTION get_hierarchical_employees(manager_user_id text)
RETURNS TABLE(
  id uuid,
  full_name text,
  email character varying,
  bio text,
  linkedin_url character varying,
  twitter_url character varying,
  telegram_url character varying,
  job_title text,  -- Job title from employees table (was 'role')
  user_role appy_user_role,  -- System role from managers table (new field)
  rate appy_compensation_type,
  department_id uuid,
  department_name text,
  manager_id text,
  manager_name text,
  active boolean,
  hierarchy_level integer,
  created_at timestamp with time zone,
  updated_at timestamp with time zone
)
LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN QUERY
  WITH RECURSIVE employee_hierarchy AS (
    -- Base case: Direct employees of the manager
    SELECT 
      e.id,
      e.full_name,
      e.email,
      e.bio,
      e.linkedin_url,
      e.twitter_url,
      e.telegram_url,
      e.role as job_title,  -- Job title from employees table
      COALESCE(mgr.role, 'manager'::appy_user_role) as user_role,  -- System role from managers table with fallback
      e.rate,
      e.department_id,
      d.name as department_name,
      e.manager_id,
      m.full_name as manager_name,
      e.active,
      1 as hierarchy_level,
      e.created_at,
      e.updated_at
    FROM appy_employees e
    LEFT JOIN appy_departments d ON e.department_id = d.id
    LEFT JOIN appy_managers m ON e.manager_id = m.user_id
    LEFT JOIN appy_managers mgr ON e.email = mgr.email AND mgr.active = true  -- Get user role if employee is also a manager
    WHERE e.manager_id = get_hierarchical_employees.manager_user_id
      AND e.active = true
    
    UNION ALL
    
    -- Recursive case: Employees of managers in the hierarchy
    SELECT 
      e2.id,
      e2.full_name,
      e2.email,
      e2.bio,
      e2.linkedin_url,
      e2.twitter_url,
      e2.telegram_url,
      e2.role as job_title,  -- Job title from employees table
      COALESCE(mgr2.role, 'manager'::appy_user_role) as user_role,  -- System role from managers table with fallback
      e2.rate,
      e2.department_id,
      d2.name as department_name,
      e2.manager_id,
      m2.full_name as manager_name,
      e2.active,
      eh.hierarchy_level + 1,
      e2.created_at,
      e2.updated_at
    FROM appy_employees e2
    LEFT JOIN appy_departments d2 ON e2.department_id = d2.id
    LEFT JOIN appy_managers m2 ON e2.manager_id = m2.user_id
    LEFT JOIN appy_managers mgr2 ON e2.email = mgr2.email AND mgr2.active = true  -- Get user role if employee is also a manager
    INNER JOIN employee_hierarchy eh ON e2.manager_id = (
      SELECT user_id FROM appy_managers WHERE appy_managers.full_name = eh.full_name AND appy_managers.active = true
    )
    WHERE e2.active = true
      AND eh.hierarchy_level < 10  -- Prevent infinite recursion
  )
  
  SELECT 
    eh.id,
    eh.full_name,
    eh.email,
    eh.bio,
    eh.linkedin_url,
    eh.twitter_url,
    eh.telegram_url,
    eh.job_title,
    eh.user_role,
    eh.rate,
    eh.department_id,
    eh.department_name,
    eh.manager_id,
    eh.manager_name,
    eh.active,
    eh.hierarchy_level,
    eh.created_at,
    eh.updated_at
  FROM employee_hierarchy eh
  ORDER BY eh.hierarchy_level, eh.full_name;
END;
$function$;

-- Step 4: Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_hierarchical_employees(text) TO anon;
GRANT EXECUTE ON FUNCTION get_hierarchical_employees(text) TO authenticated;

-- Step 5: Validation tests
DO $$
DECLARE
    test_result RECORD;
    test_count INTEGER;
BEGIN
    -- Test 1: Check function returns data without errors
    SELECT COUNT(*) INTO test_count 
    FROM get_hierarchical_employees('user_30HGqOF637XI2VQ937Jp16fiodG');  -- Mariangelica's ID from the logs
    
    RAISE NOTICE 'Test 1 - Function execution: SUCCESS, returned % rows', test_count;
    
    -- Test 2: Check return types are correct
    SELECT job_title, user_role INTO test_result
    FROM get_hierarchical_employees('user_30HGqOF637XI2VQ937Jp16fiodG')
    LIMIT 1;
    
    IF FOUND THEN
        RAISE NOTICE 'Test 2 - Return types: SUCCESS, job_title=%, user_role=%', test_result.job_title, test_result.user_role;
    ELSE
        RAISE NOTICE 'Test 2 - Return types: No data returned for test manager';
    END IF;
    
    RAISE NOTICE 'Function fix completed successfully at %', NOW();
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Validation test failed: %', SQLERRM;
    RAISE EXCEPTION 'Function fix validation failed';
END $$;

-- Step 6: Log completion
SELECT 
    'get_hierarchical_employees function fix completed' as status,
    NOW() as completed_at,
    'Fixed type mismatch between varchar job titles and appy_user_role enum' as description;
