#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to sync <PERSON><PERSON><PERSON>'s Clerk ID in the employee record
 * The employee record still has clerk_user_id: null which might be causing duplicate display
 */

import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const khodrData = {
  email: '<EMAIL>',
  clerkId: 'user_30pNedmlrT0FmilnOOjprh04Ezt',
  fullName: '<PERSON><PERSON><PERSON>'
}

async function syncKhodrEmployeeClerkId(): Promise<boolean> {
  console.log('🔄 Syncing Khodr employee Clerk ID...')
  console.log(`📧 Email: ${khodrData.email}`)
  console.log(`🆔 Clerk ID: ${khodrData.clerkId}`)

  try {
    // Step 1: Check current employee record
    const { data: employeeRecord, error: empError } = await supabase
      .from('appy_employees')
      .select('*')
      .eq('email', khodrData.email)
      .single()

    if (empError) {
      console.error(`❌ Failed to find employee record: ${empError.message}`)
      return false
    }

    console.log(`📋 Found employee record:`)
    console.log(`   ID: ${employeeRecord.id}`)
    console.log(`   Name: ${employeeRecord.full_name}`)
    console.log(`   Current Clerk ID: ${employeeRecord.clerk_user_id || 'null'}`)
    console.log(`   Role Rank: ${employeeRecord.role_rank}`)

    // Step 2: Update employee record with correct Clerk ID
    const { error: updateError } = await supabase
      .from('appy_employees')
      .update({
        clerk_user_id: khodrData.clerkId,
        full_name: khodrData.fullName
      })
      .eq('email', khodrData.email)

    if (updateError) {
      console.error(`❌ Failed to update employee record: ${updateError.message}`)
      return false
    }

    console.log(`✅ Updated employee record with Clerk ID`)

    // Step 3: Verify the update
    const { data: verifyRecord, error: verifyError } = await supabase
      .from('appy_employees')
      .select('id, full_name, email, clerk_user_id, role_rank')
      .eq('email', khodrData.email)
      .single()

    if (verifyError) {
      console.error(`❌ Failed to verify update: ${verifyError.message}`)
      return false
    }

    console.log(`\n🎉 Successfully synced Khodr employee Clerk ID!`)
    console.log(`   Employee ID: ${verifyRecord.id}`)
    console.log(`   Name: ${verifyRecord.full_name}`)
    console.log(`   Email: ${verifyRecord.email}`)
    console.log(`   Clerk ID: ${verifyRecord.clerk_user_id}`)
    console.log(`   Role Rank: ${verifyRecord.role_rank}`)

    return true

  } catch (error) {
    console.error('❌ Unexpected error:', error)
    return false
  }
}

// CLI usage
if (require.main === module) {
  console.log('🚀 Khodr Employee Clerk ID Sync Script')
  console.log('=' .repeat(50))
  
  syncKhodrEmployeeClerkId()
    .then(success => {
      if (success) {
        console.log('\n✅ Sync completed successfully!')
      } else {
        console.log('\n❌ Sync failed!')
      }
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('💥 Script failed:', error)
      process.exit(1)
    })
}

export { syncKhodrEmployeeClerkId }
