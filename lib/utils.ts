import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Get display name for employee department, handling executives gracefully
 */
export function getEmployeeDepartmentDisplay(employee: { fullName?: string; departmentName?: string | null; role?: string }): string {
  if (employee.departmentName) {
    return employee.departmentName
  }

  // Handle executives and senior roles without departments more gracefully
  const name = employee.fullName?.toLowerCase() || ''
  const role = employee.role?.toLowerCase() || ''

  if (name.includes('coo') || name.includes('ceo') || name.includes('cto') ||
      role.includes('executive') || role.includes('chief') ||
      employee.fullName === '<PERSON>') {
    return 'Executive Team'
  }

  return 'Unassigned'
}

export function generateUserId(): string {
  // Generate a user ID similar to Clerk's format for compatibility
  return `user_${Math.random().toString(36).substr(2, 9)}_${Date.now()}`
}

/**
 * Get all unique departments for an employee (their own + their managers')
 */
export function getEmployeeAllDepartments(employee: {
  departmentName?: string | null
  managers?: Array<{ departmentName?: string | null }>
}): string[] {
  const departments = new Set<string>()

  // Add employee's own department
  if (employee.departmentName) {
    departments.add(employee.departmentName)
  }

  // Add manager departments
  if (employee.managers) {
    employee.managers.forEach(manager => {
      if (manager.departmentName) {
        departments.add(manager.departmentName)
      }
    })
  }

  return Array.from(departments).sort()
}

/**
 * Format multi-department display for an employee
 * Returns format like "Sales + Engineering, Marketing" or just "Sales" if only one
 */
export function formatMultiDepartmentDisplay(employee: {
  departmentName?: string | null
  managers?: Array<{ departmentName?: string | null }>
}): string {
  const allDepartments = getEmployeeAllDepartments(employee)

  if (allDepartments.length === 0) {
    return getEmployeeDepartmentDisplay(employee)
  }

  if (allDepartments.length === 1) {
    return allDepartments[0]
  }

  // If employee has their own department, show it first with +
  const employeeDept = employee.departmentName
  if (employeeDept && allDepartments.includes(employeeDept)) {
    const otherDepts = allDepartments.filter(d => d !== employeeDept)
    if (otherDepts.length > 0) {
      return `${employeeDept} + ${otherDepts.join(', ')}`
    }
    return employeeDept
  }

  // If no employee department, just join manager departments
  return allDepartments.join(', ')
}

/**
 * Get summary of manager departments with primary/secondary distinction
 */
export function getManagerDepartmentSummary(managers: Array<{
  managerName?: string
  departmentName?: string | null
  isPrimary?: boolean
}>): string {
  if (!managers || managers.length === 0) {
    return 'No managers assigned'
  }

  const managerInfo = managers
    .filter(m => m.managerName)
    .map(m => {
      const name = m.managerName!
      const dept = m.departmentName || 'No Dept'
      const primary = m.isPrimary ? ' (Primary)' : ''
      return `${name} (${dept})${primary}`
    })

  return managerInfo.join(', ')
}

/**
 * Get unique departments from a list, removing duplicates and nulls
 */
export function getUniqueDepartments(departments: (string | null | undefined)[]): string[] {
  const unique = new Set<string>()
  departments.forEach(dept => {
    if (dept && dept.trim()) {
      unique.add(dept.trim())
    }
  })
  return Array.from(unique).sort()
}
