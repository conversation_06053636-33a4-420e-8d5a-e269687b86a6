import { supabaseAdmin } from '../../supabase-admin'
import { debug } from '../../debug'
import type { Manager } from '../core/types'

// Managers
export async function getManagers(): Promise<Manager[]> {
  const { data, error } = await supabaseAdmin.from('appy_managers')
    .select(`
      user_id,
      full_name,
      email,
      department_id,
      active,
      manager_id,
      created_at,
      role,
      appy_departments:department_id (
        id,
        name
      )
    `)
    .eq('active', true)
    .order('full_name')

  if (error) {
    throw new Error(error.message)
  }

  return data || []
}

export async function getManagerByClerkId(clerkId: string): Promise<Manager | null> {
  debug.log('🔍 Getting manager by Clerk ID:', clerkId)

  const { data: manager, error } = await supabaseAdmin.from('appy_managers')
    .select('user_id, full_name, email, department_id, active, manager_id, created_at, role')
    .eq('user_id', clerkId)
    .eq('active', true)
    .single()

  if (error) {
    debug.log('❌ Manager not found for Clerk ID:', clerkId)
    return null
  }

  debug.log('✅ Found manager:', manager.full_name)
  return manager
}

export async function createManager(userId: string, fullName: string, email: string, departmentId?: string): Promise<Manager> {
  const { data, error } = await supabaseAdmin.from('appy_managers')
    .insert({
      user_id: userId,
      full_name: fullName,
      email: email,
      department_id: departmentId || null
    })
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function ensureManagerExists(userId: string, fullName: string, email: string): Promise<void> {
  // Check if manager already exists
  const { data: existingManager } = await supabaseAdmin.from('appy_managers')
    .select('user_id')
    .eq('user_id', userId)
    .single()
  
  // If manager doesn't exist, create them
  if (!existingManager) {
    const { error } = await supabaseAdmin.from('appy_managers')
      .insert({
        user_id: userId,
        full_name: fullName,
        email: email,
        active: true
      })
    
    if (error) {
      throw new Error(`Failed to create manager: ${error.message}`)
    }
  }
}