import { supabaseAdmin } from '../../supabase-admin'
import type { Appraisal } from '../core/types'

// Revision Functions
export async function createAppraisalRevision(originalAppraisalId: string, newData: {
  question1?: string | null
  question2?: string | null
  question3?: string | null
  question4?: string | null
  question5?: string | null
  paymentStatus?: string | null
  // New fields
  keyContributions?: string | null
  extraInitiatives?: string | null
  performanceLacking?: string | null
  disciplineRating?: number | null
  disciplineComment?: string | null
  daysOffTaken?: number | null
  impactRating?: number | null
  impactComment?: string | null
  qualityRating?: number | null
  qualityComment?: string | null
  collaborationRating?: number | null
  collaborationComment?: string | null
  skillGrowthRating?: number | null
  skillGrowthComment?: string | null
  readinessPromotion?: string | null
  readinessComment?: string | null
  compensationRecommendation?: string | null
}): Promise<Appraisal> {
  // First, get the original appraisal
  const { data: originalAppraisal, error: fetchError } = await supabaseAdmin
    .from('appy_appraisals')
    .select('*')
    .eq('id', originalAppraisalId)
    .single()
  
  if (fetchError || !originalAppraisal) {
    throw new Error('Original appraisal not found')
  }

  // Check if this is the first revision (need to set original_submission_date)
  const isFirstRevision = !originalAppraisal.is_revision && originalAppraisal.status === 'submitted'
  const newRevisionNumber = (originalAppraisal.revision_number || 1) + 1

  // Update the original appraisal to create a revision
  const updateData: any = {
    question_1: newData.question1 !== undefined ? newData.question1 : originalAppraisal.question_1,
    question_2: newData.question2 !== undefined ? newData.question2 : originalAppraisal.question_2,
    question_3: newData.question3 !== undefined ? newData.question3 : originalAppraisal.question_3,
    question_4: newData.question4 !== undefined ? newData.question4 : originalAppraisal.question_4,
    question_5: newData.question5 !== undefined ? newData.question5 : originalAppraisal.question_5,
    payment_status: newData.paymentStatus !== undefined ? newData.paymentStatus : originalAppraisal.payment_status,
    // New fields
    key_contributions: newData.keyContributions !== undefined ? newData.keyContributions : originalAppraisal.key_contributions,
    extra_initiatives: newData.extraInitiatives !== undefined ? newData.extraInitiatives : originalAppraisal.extra_initiatives,
    performance_lacking: newData.performanceLacking !== undefined ? newData.performanceLacking : originalAppraisal.performance_lacking,
    discipline_rating: newData.disciplineRating !== undefined ? newData.disciplineRating : originalAppraisal.discipline_rating,
    discipline_comment: newData.disciplineComment !== undefined ? newData.disciplineComment : originalAppraisal.discipline_comment,
    days_off_taken: newData.daysOffTaken !== undefined ? newData.daysOffTaken : originalAppraisal.days_off_taken,
    impact_rating: newData.impactRating !== undefined ? newData.impactRating : originalAppraisal.impact_rating,
    impact_comment: newData.impactComment !== undefined ? newData.impactComment : originalAppraisal.impact_comment,
    quality_rating: newData.qualityRating !== undefined ? newData.qualityRating : originalAppraisal.quality_rating,
    quality_comment: newData.qualityComment !== undefined ? newData.qualityComment : originalAppraisal.quality_comment,
    collaboration_rating: newData.collaborationRating !== undefined ? newData.collaborationRating : originalAppraisal.collaboration_rating,
    collaboration_comment: newData.collaborationComment !== undefined ? newData.collaborationComment : originalAppraisal.collaboration_comment,
    skill_growth_rating: newData.skillGrowthRating !== undefined ? newData.skillGrowthRating : originalAppraisal.skill_growth_rating,
    skill_growth_comment: newData.skillGrowthComment !== undefined ? newData.skillGrowthComment : originalAppraisal.skill_growth_comment,
    readiness_promotion: newData.readinessPromotion !== undefined ? newData.readinessPromotion : originalAppraisal.readiness_promotion,
    readiness_comment: newData.readinessComment !== undefined ? newData.readinessComment : originalAppraisal.readiness_comment,
    compensation_recommendation: newData.compensationRecommendation !== undefined ? newData.compensationRecommendation : originalAppraisal.compensation_recommendation,
    status: 'pending', // Reset to draft status for editing
    revision_number: newRevisionNumber,
    is_revision: true,
    last_edited_at: new Date().toISOString(),
    submitted_at: null // Clear submitted timestamp
  }

  // Set original submission date if this is the first revision
  if (isFirstRevision) {
    updateData.original_submission_date = originalAppraisal.submitted_at
  }

  const { data, error } = await supabaseAdmin.from('appy_appraisals')
    .update(updateData)
    .eq('id', originalAppraisalId)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to create appraisal revision: ${error.message}`)
  }
  
  return data
}

export async function updateAppraisalRevision(id: string, updateData: {
  question1?: string | null
  question2?: string | null
  question3?: string | null
  question4?: string | null
  question5?: string | null
  paymentStatus?: string | null
  // New fields
  keyContributions?: string | null
  extraInitiatives?: string | null
  performanceLacking?: string | null
  disciplineRating?: number | null
  disciplineComment?: string | null
  daysOffTaken?: number | null
  impactRating?: number | null
  impactComment?: string | null
  qualityRating?: number | null
  qualityComment?: string | null
  collaborationRating?: number | null
  collaborationComment?: string | null
  skillGrowthRating?: number | null
  skillGrowthComment?: string | null
  readinessPromotion?: string | null
  readinessComment?: string | null
  compensationRecommendation?: string | null
}): Promise<Appraisal> {
  const { data, error } = await supabaseAdmin.from('appy_appraisals')
    .update({
      question_1: updateData.question1,
      question_2: updateData.question2,
      question_3: updateData.question3,
      question_4: updateData.question4,
      question_5: updateData.question5,
      payment_status: updateData.paymentStatus,
      // New fields
      key_contributions: updateData.keyContributions,
      extra_initiatives: updateData.extraInitiatives,
      performance_lacking: updateData.performanceLacking,
      discipline_rating: updateData.disciplineRating,
      discipline_comment: updateData.disciplineComment,
      days_off_taken: updateData.daysOffTaken,
      impact_rating: updateData.impactRating,
      impact_comment: updateData.impactComment,
      quality_rating: updateData.qualityRating,
      quality_comment: updateData.qualityComment,
      collaboration_rating: updateData.collaborationRating,
      collaboration_comment: updateData.collaborationComment,
      skill_growth_rating: updateData.skillGrowthRating,
      skill_growth_comment: updateData.skillGrowthComment,
      readiness_promotion: updateData.readinessPromotion,
      readiness_comment: updateData.readinessComment,
      compensation_recommendation: updateData.compensationRecommendation,
      last_edited_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to update appraisal revision: ${error.message}`)
  }

  return data
}

export async function resubmitAppraisalRevision(id: string): Promise<Appraisal> {
  const { data, error } = await supabaseAdmin.from('appy_appraisals')
    .update({
      status: 'submitted',
      submitted_at: new Date().toISOString(),
      last_edited_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    throw new Error(`Failed to resubmit appraisal revision: ${error.message}`)
  }

  return data
}