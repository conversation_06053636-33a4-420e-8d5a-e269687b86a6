import { supabaseAdmin } from '../../supabase-admin'
import type { Department } from '../core/types'

// Departments
export async function getDepartments(): Promise<Department[]> {
  const { data, error } = await supabaseAdmin.from('appy_departments')
    .select('id, name, created_at')
    .order('name')
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data || []
}

export async function createDepartment(name: string): Promise<Department> {
  const { data, error } = await supabaseAdmin.from('appy_departments')
    .insert({ name })
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function updateDepartment(id: string, name: string): Promise<Department> {
  const { data, error } = await supabaseAdmin.from('appy_departments')
    .update({ name })
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function deleteDepartment(id: string): Promise<void> {
  const { error } = await supabaseAdmin.from('appy_departments')
    .delete()
    .eq('id', id)
  
  if (error) {
    throw new Error(error.message)
  }
}