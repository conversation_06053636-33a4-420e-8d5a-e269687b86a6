import { supabaseAdmin } from '../../supabase-admin'

// PTO (Paid Time Off) Functions
export async function getPTOBalance(employeeId: string, year?: number): Promise<any> {
  const currentYear = year || new Date().getFullYear()
  
  const { data, error } = await supabaseAdmin
    .from('appy_employee_pto_balances')
    .select('*')
    .eq('employee_id', employeeId)
    .eq('year', currentYear)
    .single()
  
  if (error) {
    if (error.code === 'PGRST116') {
      // No balance record found, create one with default 7 days
      return await createPTOBalance(employeeId, currentYear)
    }
    throw new Error(error.message)
  }
  
  return data
}

export async function createPTOBalance(employeeId: string, year: number, totalDays: number = 7): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_employee_pto_balances')
    .insert({
      employee_id: employeeId,
      year: year,
      total_days: totalDays,
      used_days: 0
    })
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function updatePTOBalance(employeeId: string, usedDays: number, year?: number): Promise<any> {
  const currentYear = year || new Date().getFullYear()
  
  const { data, error } = await supabaseAdmin
    .from('appy_employee_pto_balances')
    .update({
      used_days: usedDays,
      updated_at: new Date().toISOString()
    })
    .eq('employee_id', employeeId)
    .eq('year', currentYear)
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function createPTORequest(requestData: {
  employeeId: string
  managerId: string
  requestType: 'vacation' | 'sick' | 'personal' | 'emergency'
  startDate: string
  endDate: string
  daysRequested: number
  reason?: string
}): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_pto_requests')
    .insert({
      employee_id: requestData.employeeId,
      manager_id: requestData.managerId,
      request_type: requestData.requestType,
      start_date: requestData.startDate,
      end_date: requestData.endDate,
      days_requested: requestData.daysRequested,
      reason: requestData.reason || null,
      status: 'pending'
    })
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function getPTORequests(filters?: {
  employeeId?: string
  managerId?: string
  status?: 'pending' | 'approved' | 'rejected' | 'cancelled'
  year?: number
}): Promise<any[]> {
  let query = supabaseAdmin
    .from('appy_pto_requests')
    .select(`
      *,
      appy_employees!inner (
        id,
        full_name,
        appy_departments!inner (
          name
        )
      )
    `)
    .order('created_at', { ascending: false })
  
  if (filters?.employeeId) {
    query = query.eq('employee_id', filters.employeeId)
  }
  
  if (filters?.managerId) {
    query = query.eq('manager_id', filters.managerId)
  }
  
  if (filters?.status) {
    query = query.eq('status', filters.status)
  }
  
  if (filters?.year) {
    const startOfYear = `${filters.year}-01-01`
    const endOfYear = `${filters.year}-12-31`
    query = query.gte('start_date', startOfYear).lte('start_date', endOfYear)
  }
  
  const { data, error } = await query
  
  if (error) {
    throw new Error(error.message)
  }
  
  return (data || []).map(request => ({
    ...request,
    employee_name: request.appy_employees?.full_name || '',
    manager_name: 'Manager', // Remove manager join for now
    department_name: request.appy_employees?.appy_departments?.name || ''
  }))
}

export async function getPTORequestById(requestId: string): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_pto_requests')
    .select(`
      *,
      appy_employees!inner (
        id,
        full_name,
        appy_departments!inner (
          name
        )
      ),
      appy_managers!inner (
        full_name
      )
    `)
    .eq('id', requestId)
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return {
    ...data,
    employee_name: data.appy_employees?.full_name || '',
    manager_name: data.appy_managers?.full_name || '',
    department_name: data.appy_employees?.appy_departments?.name || ''
  }
}

export async function approvePTORequest(requestId: string, approverId: string): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_pto_requests')
    .update({
      status: 'approved',
      approved_by: approverId,
      approved_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('id', requestId)
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function rejectPTORequest(requestId: string, approverId: string, reason: string): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_pto_requests')
    .update({
      status: 'rejected',
      approved_by: approverId,
      approved_at: new Date().toISOString(),
      rejected_reason: reason,
      updated_at: new Date().toISOString()
    })
    .eq('id', requestId)
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function cancelPTORequest(requestId: string): Promise<any> {
  const { data, error } = await supabaseAdmin
    .from('appy_pto_requests')
    .update({
      status: 'cancelled',
      updated_at: new Date().toISOString()
    })
    .eq('id', requestId)
    .select()
    .single()
  
  if (error) {
    throw new Error(error.message)
  }
  
  return data
}

export async function getPTOStats(managerId?: string, year?: number): Promise<any> {
  const currentYear = year || new Date().getFullYear()
  
  let query = supabaseAdmin
    .from('appy_pto_requests')
    .select('status')
    .gte('start_date', `${currentYear}-01-01`)
    .lte('start_date', `${currentYear}-12-31`)
  
  if (managerId) {
    query = query.eq('manager_id', managerId)
  }
  
  const { data, error } = await query
  
  if (error) {
    throw new Error(error.message)
  }
  
  const stats = {
    totalRequests: data?.length || 0,
    pendingRequests: data?.filter(r => r.status === 'pending').length || 0,
    approvedRequests: data?.filter(r => r.status === 'approved').length || 0,
    rejectedRequests: data?.filter(r => r.status === 'rejected').length || 0,
  }
  
  return stats
}

export async function checkPTOAvailability(employeeId: string, daysRequested: number, year?: number): Promise<boolean> {
  const currentYear = year || new Date().getFullYear()
  
  try {
    const balance = await getPTOBalance(employeeId, currentYear)
    return balance.available_days >= daysRequested
  } catch (error) {
    return false
  }
}

export async function initializePTOBalances(employeeIds: string[], year?: number, totalDays: number = 7): Promise<void> {
  const currentYear = year || new Date().getFullYear()
  
  const balances = employeeIds.map(employeeId => ({
    employee_id: employeeId,
    year: currentYear,
    total_days: totalDays,
    used_days: 0
  }))
  
  const { error } = await supabaseAdmin
    .from('appy_employee_pto_balances')
    .upsert(balances, {
      onConflict: 'employee_id,year'
    })
  
  if (error) {
    throw new Error(error.message)
  }
}