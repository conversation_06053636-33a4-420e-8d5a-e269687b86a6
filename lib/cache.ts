// Legacy cache interface for backward compatibility
import { enhancedCache, CACHE_DURATIONS, CacheEventManager } from './cache-enhanced'

export class SimpleCache {
  get<T>(key: string): T | null {
    return enhancedCache.get<T>(key)
  }
  
  set<T>(key: string, data: T, ttl: number = 60000): void {
    // Determine cache type based on key patterns for better organization
    let cacheType = 'general'
    if (key.includes('employee')) cacheType = 'employees'
    else if (key.includes('manager')) cacheType = 'managers'  
    else if (key.includes('department')) cacheType = 'departments'
    else if (key.includes('performance')) cacheType = 'performance-stats'
    else if (key.includes('appraisal')) cacheType = 'appraisals'
    else if (key.includes('period')) cacheType = 'periods'
    
    enhancedCache.set(key, data, ttl, cacheType)
  }
  
  clear(): void {
    enhancedCache.clear()
  }
  
  delete(key: string): void {
    enhancedCache.delete(key)
  }
}

// Global cache instance (maintains backward compatibility)
export const cache = new SimpleCache()

// Export enhanced cache features
export { enhancedCache, CACHE_DURATIONS, CacheEventManager }

// Cache invalidation functions
export function invalidatePerformanceStatsCache(managerId?: string): void {
  // console.log('🗑️ Invalidating performance stats cache')
  
  // Clear global stats cache
  cache.delete('global-performance-stats')
  
  // Clear specific manager cache if provided
  if (managerId) {
    cache.delete(`manager-performance-stats-${managerId}`)
  } else {
    // Clear all manager caches (brute force approach)
    // In a real app, you might want to track active manager IDs
    cache.clear()
  }
}

export function invalidatePTOCache(managerId?: string): void {
  // console.log('🗑️ Invalidating PTO cache')
  
  // Clear PTO-specific caches
  cache.delete('pto-dashboard-data')
  cache.delete('pto-stats')
  
  if (managerId) {
    cache.delete(`pto-manager-data-${managerId}`)
    cache.delete(`pto-stats-${managerId}`)
  } else {
    // Clear all PTO caches (brute force approach)
    cache.clear()
  }
}

export function clearAllCache(): void {
  // console.log('🗑️ Clearing all cache')
  cache.clear()
}