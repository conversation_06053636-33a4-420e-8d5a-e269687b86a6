import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from './auth'
import { canAccessAdminPage } from './admin-data'
import { supabaseAdmin } from './supabase-admin'

export async function adminMiddleware(request: NextRequest, auth?: any) {
  const url = new URL(request.url)
  const pathname = url.pathname
  
  // Check if this is an admin route
  const adminRouteMatch = pathname.match(/^\/dashboard\/admin\/([^\/]+)$/)
  
  if (adminRouteMatch) {
    const adminId = adminRouteMatch[1]

    try {
      let currentUser

      if (auth) {
        // Use auth from middleware context
        const { userId } = await auth()

        if (!userId) {
          return NextResponse.redirect(new URL('/sign-in', request.url))
        }

        // Get user role from unified appy_managers table
        const { data: managerData } = await supabaseAdmin
          .from('appy_managers')
          .select('role')
          .eq('user_id', userId)
          .single()

        const role = managerData?.role || 'manager'

        currentUser = {
          id: userId,
          role: role as any
        }
      } else {
        // Fallback to getCurrentUser for non-middleware contexts
        currentUser = await getCurrentUser()
      }

      if (!currentUser) {
        // Redirect to sign-in if not authenticated
        return NextResponse.redirect(new URL('/sign-in', request.url))
      }

      // Check if user has admin or super-admin role
      if (!['admin', 'super-admin'].includes(currentUser.role)) {
        // Redirect to dashboard with error if insufficient role
        return NextResponse.redirect(new URL('/dashboard?error=insufficient_permissions', request.url))
      }

      // Check if user can access this specific admin page
      const canAccess = await canAccessAdminPage(adminId, currentUser.id, currentUser.role)

      if (!canAccess) {
        // Redirect to dashboard with access denied error
        return NextResponse.redirect(new URL('/dashboard?error=access_denied', request.url))
      }

      // Allow access
      return NextResponse.next()

    } catch (error) {
      console.error('Admin middleware error:', error)
      // Redirect to sign-in on error
      return NextResponse.redirect(new URL('/sign-in', request.url))
    }
  }
  
  // Not an admin route, continue normally
  return NextResponse.next()
}

export function isAdminRoute(pathname: string): boolean {
  return /^\/dashboard\/admin\/[^\/]+$/.test(pathname)
}