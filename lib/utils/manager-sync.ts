/**
 * Utility functions to sync manager data between legacy and new systems
 */

import { supabaseAdminQuery } from '../supabase-admin'
import { db } from '../db'

/**
 * Sync legacy manager_id to appy_employee_managers table for employees missing manager relationships
 * This ensures backward compatibility and data consistency
 */
export async function syncLegacyManagerData(): Promise<{
  success: boolean
  syncedCount: number
  errors: string[]
}> {
  console.log('🔄 [MANAGER SYNC] Starting legacy manager data sync...')
  
  const errors: string[] = []
  let syncedCount = 0

  try {
    // Find employees with manager_id but no entries in appy_employee_managers
    const { data: employeesWithLegacyManagers, error: queryError } = await supabaseAdminQuery
      .employees()
      .select('id, full_name, manager_id')
      .not('manager_id', 'is', null)

    if (queryError) {
      console.error('🔄 [MANAGER SYNC] Failed to query employees:', queryError)
      return { success: false, syncedCount: 0, errors: [queryError.message] }
    }

    console.log('🔄 [MANAGER SYNC] Found', employeesWithLegacyManagers?.length || 0, 'employees with legacy manager_id')

    if (!employeesWithLegacyManagers || employeesWithLegacyManagers.length === 0) {
      console.log('🔄 [MANAGER SYNC] No employees with legacy manager_id found')
      return { success: true, syncedCount: 0, errors: [] }
    }

    // Check each employee to see if they have manager relationships
    for (const employee of employeesWithLegacyManagers) {
      try {
        console.log('🔄 [MANAGER SYNC] Checking employee:', employee.full_name, 'ID:', employee.id)
        
        // Check if employee already has manager relationships
        const existingManagers = await db.getEmployeeManagers(employee.id)
        
        if (existingManagers.length > 0) {
          console.log('🔄 [MANAGER SYNC] Employee', employee.full_name, 'already has', existingManagers.length, 'manager relationships, skipping')
          continue
        }

        // Employee has legacy manager_id but no new manager relationships - sync it
        console.log('🔄 [MANAGER SYNC] Syncing legacy manager for employee:', employee.full_name, 'Manager ID:', employee.manager_id)
        
        // Verify the manager exists
        const { data: manager, error: managerError } = await supabaseAdminQuery
          .managers()
          .select('user_id, full_name')
          .eq('user_id', employee.manager_id)
          .single()

        if (managerError || !manager) {
          console.warn('🔄 [MANAGER SYNC] Manager not found for employee', employee.full_name, 'Manager ID:', employee.manager_id)
          errors.push(`Manager ${employee.manager_id} not found for employee ${employee.full_name}`)
          continue
        }

        // Create the manager relationship
        await db.assignManagerToEmployee(employee.id, employee.manager_id, true) // Set as primary
        
        console.log('🔄 [MANAGER SYNC] Successfully synced manager', manager.full_name, 'for employee', employee.full_name)
        syncedCount++
        
      } catch (error) {
        console.error('🔄 [MANAGER SYNC] Error syncing employee', employee.full_name, ':', error)
        errors.push(`Failed to sync employee ${employee.full_name}: ${error}`)
      }
    }

    console.log('🔄 [MANAGER SYNC] Sync completed. Synced:', syncedCount, 'Errors:', errors.length)
    
    return {
      success: true,
      syncedCount,
      errors
    }

  } catch (error) {
    console.error('🔄 [MANAGER SYNC] Fatal error during sync:', error)
    return {
      success: false,
      syncedCount,
      errors: [`Fatal error: ${error}`]
    }
  }
}

/**
 * Ensure a specific employee has their manager relationships synced
 * Called when loading employee profile to ensure data consistency
 */
export async function ensureEmployeeManagerSync(employeeId: string): Promise<boolean> {
  try {
    console.log('🔄 [MANAGER SYNC] Ensuring manager sync for employee:', employeeId)
    
    // Check if employee has manager relationships
    const existingManagers = await db.getEmployeeManagers(employeeId)
    
    if (existingManagers.length > 0) {
      console.log('🔄 [MANAGER SYNC] Employee already has', existingManagers.length, 'manager relationships')
      return true
    }

    // Get employee's legacy manager_id
    const { data: employee, error } = await supabaseAdminQuery
      .employees()
      .select('id, full_name, manager_id')
      .eq('id', employeeId)
      .single()

    if (error || !employee) {
      console.error('🔄 [MANAGER SYNC] Employee not found:', employeeId)
      return false
    }

    if (!employee.manager_id) {
      console.log('🔄 [MANAGER SYNC] Employee has no legacy manager_id, nothing to sync')
      return true
    }

    // Sync the legacy manager
    console.log('🔄 [MANAGER SYNC] Syncing legacy manager for employee:', employee.full_name)
    await db.assignManagerToEmployee(employee.id, employee.manager_id, true)
    
    console.log('🔄 [MANAGER SYNC] Successfully synced legacy manager for employee:', employee.full_name)
    return true

  } catch (error) {
    console.error('🔄 [MANAGER SYNC] Error ensuring employee manager sync:', error)
    return false
  }
}
