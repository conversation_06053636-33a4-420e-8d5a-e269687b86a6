"use server"

import { supabaseAdmin } from '../supabase-admin'
import type { HierarchyNode } from '../types'

export async function getHierarchyData(): Promise<HierarchyNode[]> {
  try {
    // Get all employees with their hierarchy roles and reporting relationships
    const { data: employees, error: employeesError } = await supabaseAdmin
      .from('appy_employees')
      .select(`
        id,
        full_name,
        role_rank,
        manager_id,
        department_id,
        departments:department_id (
          name
        )
      `)
      .eq('active', true)
      .order('full_name')

    if (employeesError) {
      console.error('Error fetching employees:', employeesError)
      return []
    }

    // Build hierarchy tree using single source of truth
    const hierarchyMap = new Map<string, HierarchyNode>()
    
    // Create nodes for all people, determining type based on role_rank
    employees.forEach(employee => {
      const isManager = employee.role_rank !== 'employee'
      
      hierarchyMap.set(employee.id, {
        id: employee.id,
        name: employee.full_name,
        role: employee.role_rank,
        department: (employee.departments as any)?.name || 'Unknown',
        type: isManager ? 'manager' : 'employee',
        children: [],
        parentId: employee.manager_id
      })
    })

    // Build the tree structure by connecting employees to their managers
    const rootNodes: HierarchyNode[] = []
    
    hierarchyMap.forEach(node => {
      if (node.parentId && hierarchyMap.has(node.parentId)) {
        // This person has a manager - add them as a child
        const parent = hierarchyMap.get(node.parentId)!
        parent.children.push(node)
      } else {
        // This is a root node (no parent manager or manager not in employee table)
        rootNodes.push(node)
      }
    })

    console.log(`✅ [HIERARCHY] Built hierarchy with ${hierarchyMap.size} people, ${rootNodes.length} root nodes`)
    
    return rootNodes
  } catch (error) {
    console.error('Failed to fetch hierarchy data:', error)
    return []
  }
}

export async function getDepartmentHierarchy(departmentId: string): Promise<HierarchyNode[]> {
  try {
    const { data: employees, error } = await supabaseAdmin
      .from('appy_employees')
      .select(`
        id,
        full_name,
        role_rank,
        manager_id,
        departments:department_id (
          name
        )
      `)
      .eq('department_id', departmentId)
      .eq('active', true)
      .order('full_name')

    if (error) {
      console.error('Error fetching department hierarchy:', error)
      return []
    }

    // Build department-specific hierarchy using single source
    const hierarchyMap = new Map<string, HierarchyNode>()
    
    employees.forEach(employee => {
      const isManager = employee.role_rank !== 'employee'
      
      hierarchyMap.set(employee.id, {
        id: employee.id,
        name: employee.full_name,
        role: employee.role_rank,
        department: (employee.departments as any)?.name || 'Unknown',
        type: isManager ? 'manager' : 'employee',
        children: [],
        parentId: employee.manager_id
      })
    })

    // Connect parent-child relationships within the department
    const rootNodes: HierarchyNode[] = []
    
    hierarchyMap.forEach(node => {
      if (node.parentId && hierarchyMap.has(node.parentId)) {
        const parent = hierarchyMap.get(node.parentId)!
        parent.children.push(node)
      } else {
        rootNodes.push(node)
      }
    })

    return rootNodes
  } catch (error) {
    console.error('Failed to fetch department hierarchy:', error)
    return []
  }
}