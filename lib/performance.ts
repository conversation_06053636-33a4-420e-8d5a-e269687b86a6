/**
 * Performance monitoring and optimization utilities
 */

// Performance metrics tracking
interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  type: 'timing' | 'counter' | 'gauge'
  tags?: Record<string, string>
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private timers: Map<string, number> = new Map()
  private observers: PerformanceObserver[] = []

  constructor() {
    this.setupObservers()
    console.log('[PERF] Performance monitor initialized')
  }

  private setupObservers() {
    if (typeof window === 'undefined') return

    try {
      // Enhanced error handling for performance observers
      const safeObserverCallback = (callback: (list: PerformanceObserverEntryList) => void) => {
        return (list: PerformanceObserverEntryList) => {
          try {
            callback(list)
          } catch (error) {
            console.warn('[PERF] Observer callback error:', error)
          }
        }
      }

      // Observe navigation timing with enhanced error handling
      const navObserver = new PerformanceObserver(safeObserverCallback((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming
            this.recordMetric('page_load_time', navEntry.loadEventEnd - navEntry.startTime, 'timing')
            this.recordMetric('dom_content_loaded', navEntry.domContentLoadedEventEnd - navEntry.startTime, 'timing')
            this.recordMetric('first_paint', navEntry.responseStart - navEntry.startTime, 'timing')
          }
        }
      }))
      navObserver.observe({ entryTypes: ['navigation'] })
      this.observers.push(navObserver)

      // Observe resource timing with enhanced error handling
      const resourceObserver = new PerformanceObserver(safeObserverCallback((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming
            this.recordMetric('resource_load_time', resourceEntry.responseEnd - resourceEntry.startTime, 'timing', {
              resource_type: resourceEntry.initiatorType,
              resource_name: resourceEntry.name
            })
          }
        }
      }))
      resourceObserver.observe({ entryTypes: ['resource'] })
      this.observers.push(resourceObserver)

      // Observe largest contentful paint with enhanced error handling
      const lcpObserver = new PerformanceObserver(safeObserverCallback((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('largest_contentful_paint', entry.startTime, 'timing')
        }
      }))
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      this.observers.push(lcpObserver)

      // Observe first input delay with enhanced error handling
      const fidObserver = new PerformanceObserver(safeObserverCallback((list) => {
        for (const entry of list.getEntries()) {
          const fidEntry = entry as any // Cast to any since first-input timing is not fully typed
          this.recordMetric('first_input_delay', fidEntry.processingStart - fidEntry.startTime, 'timing')
        }
      }))
      fidObserver.observe({ entryTypes: ['first-input'] })
      this.observers.push(fidObserver)

    } catch (error) {
      console.warn('[PERF] Performance observers not supported:', error)
    }
  }

  // Enhanced metric recording with validation and limits
  recordMetric(name: string, value: number, type: PerformanceMetric['type'], tags?: Record<string, string>) {
    // Validate inputs to prevent memory issues
    if (typeof name !== 'string' || name.length === 0) {
      console.warn('[PERF] Invalid metric name:', name)
      return
    }

    if (typeof value !== 'number' || !isFinite(value)) {
      console.warn('[PERF] Invalid metric value:', value)
      return
    }

    // Prevent memory leaks by limiting stored metrics
    const MAX_METRICS = 1000
    if (this.metrics.length >= MAX_METRICS) {
      // Remove oldest metrics to prevent memory growth
      this.metrics = this.metrics.slice(-MAX_METRICS / 2)
      console.warn('[PERF] Metrics buffer trimmed to prevent memory issues')
    }

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type,
      tags
    }

    this.metrics.push(metric)
    console.log(`[PERF] Recorded metric: ${name} = ${value}${type === 'timing' ? 'ms' : ''}`, tags)
  }

  startTimer(name: string) {
    this.timers.set(name, performance.now())
    console.log(`[PERF] Started timer: ${name}`)
  }

  endTimer(name: string, tags?: Record<string, string>) {
    const startTime = this.timers.get(name)
    if (startTime) {
      const duration = performance.now() - startTime
      this.recordMetric(name, duration, 'timing', tags)
      this.timers.delete(name)
      console.log(`[PERF] Ended timer: ${name} = ${duration.toFixed(2)}ms`)
      return duration
    }
    console.warn(`[PERF] Timer not found: ${name}`)
    return 0
  }

  getMetrics(name?: string): PerformanceMetric[] {
    if (name) {
      return this.metrics.filter(m => m.name === name)
    }
    return [...this.metrics]
  }

  getAverageMetric(name: string): number {
    const metrics = this.getMetrics(name)
    if (metrics.length === 0) return 0
    
    const sum = metrics.reduce((acc, m) => acc + m.value, 0)
    return sum / metrics.length
  }

  clearMetrics() {
    this.metrics = []
    this.timers.clear()
    console.log('[PERF] Cleared all metrics')
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.clearMetrics()
    console.log('[PERF] Performance monitor destroyed')
  }

  // Get performance summary
  getSummary() {
    const summary = {
      totalMetrics: this.metrics.length,
      activeTimers: this.timers.size,
      averages: {} as Record<string, number>,
      latest: {} as Record<string, number>
    }

    // Calculate averages for timing metrics
    const timingMetrics = this.metrics.filter(m => m.type === 'timing')
    const metricNames = [...new Set(timingMetrics.map(m => m.name))]
    
    metricNames.forEach(name => {
      summary.averages[name] = this.getAverageMetric(name)
      const latest = this.getMetrics(name).slice(-1)[0]
      if (latest) {
        summary.latest[name] = latest.value
      }
    })

    return summary
  }
}

// Global performance monitor instance
let performanceMonitor: PerformanceMonitor | null = null

export function getPerformanceMonitor(): PerformanceMonitor {
  if (!performanceMonitor) {
    performanceMonitor = new PerformanceMonitor()
  }
  return performanceMonitor
}

// Cached DOM node counting to prevent performance overhead
let cachedDomNodeCount = 0
let lastDomCountTime = 0
const DOM_COUNT_CACHE_DURATION = 5000 // Cache for 5 seconds

// Memory leak detection with optimized DOM counting
export function detectMemoryLeaks() {
  if (typeof window === 'undefined') return

  const monitor = getPerformanceMonitor()

  // Check for memory usage if available
  if ('memory' in performance) {
    const memory = (performance as any).memory
    monitor.recordMetric('memory_used', memory.usedJSHeapSize, 'gauge')
    monitor.recordMetric('memory_total', memory.totalJSHeapSize, 'gauge')
    monitor.recordMetric('memory_limit', memory.jsHeapSizeLimit, 'gauge')

    console.log('[PERF] Memory usage:', {
      used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
      total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
      limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`
    })
  }

  // Optimized DOM node counting with caching
  const now = Date.now()
  if (now - lastDomCountTime > DOM_COUNT_CACHE_DURATION) {
    // Only count DOM nodes every 5 seconds to prevent performance overhead
    try {
      cachedDomNodeCount = document.querySelectorAll('*').length
      lastDomCountTime = now
      console.log('[PERF] DOM node count updated:', cachedDomNodeCount)
    } catch (error) {
      console.warn('[PERF] Failed to count DOM nodes:', error)
      cachedDomNodeCount = 0
    }
  }

  monitor.recordMetric('dom_nodes', cachedDomNodeCount, 'gauge')

  if (cachedDomNodeCount > 10000) {
    console.warn('[PERF] High DOM node count detected:', cachedDomNodeCount)
  }
}

// Bundle size optimization helpers
export function measureBundleSize() {
  if (typeof window === 'undefined') return

  const monitor = getPerformanceMonitor()
  
  // Measure script sizes
  const scripts = document.querySelectorAll('script[src]')
  scripts.forEach((script, index) => {
    const src = script.getAttribute('src')
    if (src) {
      monitor.recordMetric('script_count', 1, 'counter', { script_index: index.toString() })
    }
  })

  // Measure CSS sizes
  const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')
  stylesheets.forEach((link, index) => {
    const href = link.getAttribute('href')
    if (href) {
      monitor.recordMetric('stylesheet_count', 1, 'counter', { stylesheet_index: index.toString() })
    }
  })

  console.log('[PERF] Bundle analysis:', {
    scripts: scripts.length,
    stylesheets: stylesheets.length
  })
}

// Performance timing helpers
export function withPerformanceTracking<T>(
  name: string,
  fn: () => T | Promise<T>,
  tags?: Record<string, string>
): T | Promise<T> {
  const monitor = getPerformanceMonitor()
  monitor.startTimer(name)
  
  try {
    const result = fn()
    
    if (result instanceof Promise) {
      return result.finally(() => {
        monitor.endTimer(name, tags)
      }) as T
    } else {
      monitor.endTimer(name, tags)
      return result
    }
  } catch (error) {
    monitor.endTimer(name, { ...tags, error: 'true' })
    throw error
  }
}

// React component performance tracking
export function trackComponentRender(componentName: string) {
  const monitor = getPerformanceMonitor()
  monitor.recordMetric('component_render', 1, 'counter', { component: componentName })
  console.log(`[PERF] Component rendered: ${componentName}`)
}

// Cleanup function for preventing memory leaks
export function cleanupPerformanceMonitoring() {
  if (performanceMonitor) {
    performanceMonitor.destroy()
    performanceMonitor = null
  }
}

// Performance optimization recommendations
export function getPerformanceRecommendations(): string[] {
  const monitor = getPerformanceMonitor()
  const summary = monitor.getSummary()
  const recommendations: string[] = []

  // Check page load time
  if (summary.latest.page_load_time > 3000) {
    recommendations.push('Page load time is over 3 seconds. Consider optimizing images, reducing bundle size, or implementing code splitting.')
  }

  // Check LCP
  if (summary.latest.largest_contentful_paint > 2500) {
    recommendations.push('Largest Contentful Paint is over 2.5 seconds. Optimize critical rendering path and largest content elements.')
  }

  // Check FID
  if (summary.latest.first_input_delay > 100) {
    recommendations.push('First Input Delay is over 100ms. Reduce JavaScript execution time and optimize event handlers.')
  }

  // Check memory usage
  if (summary.latest.memory_used > 50 * 1024 * 1024) { // 50MB
    recommendations.push('High memory usage detected. Check for memory leaks and optimize data structures.')
  }

  // Check DOM nodes
  if (summary.latest.dom_nodes > 5000) {
    recommendations.push('High DOM node count. Consider virtualizing large lists and optimizing component structure.')
  }

  return recommendations
}

console.log('[PERF] Performance monitoring utilities loaded')
