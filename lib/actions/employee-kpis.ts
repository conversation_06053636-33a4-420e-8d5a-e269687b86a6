"use server"

import { revalidatePath } from "next/cache"
import { supabaseAdminQuery } from "../supabase-admin"
import {
  requirePermission,
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  handleServerActionError,
  RateLimitError,
  ValidationError
} from "./shared"

export async function createEmployeeKPI(data: {
  employee_id: string
  kpi_name: string
  kpi_value?: string | null
  kpi_target?: string | null
  kpi_unit?: string | null
  period?: string | null
  description?: string | null
}) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'kpi-create', 20, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('employee:write')

    // Validate data
    if (!data.employee_id || !data.kpi_name) {
      throw new ValidationError("Employee ID and KPI name are required")
    }

    // Create KPI in database
    const { error } = await supabaseAdminQuery
      .employeeKpis()
      .insert(data)

    if (error) {
      console.error("Error creating KPI:", error)
      throw new Error("Failed to create KPI")
    }

    // Log the action
    await logUserAction('kpi:create', { 
      employeeId: data.employee_id,
      kpiName: data.kpi_name
    })

    // Revalidate cache
    revalidatePath(`/dashboard/employees/${data.employee_id}/profile`)
    
    return { success: true, message: "KPI created successfully" }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function updateEmployeeKPI(
  kpiId: string,
  data: {
    employee_id?: string
    kpi_name?: string
    kpi_value?: string | null
    kpi_target?: string | null
    kpi_unit?: string | null
    period?: string | null
    description?: string | null
  }
) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'kpi-update', 20, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('employee:write')

    // Validate KPI ID
    if (!kpiId || typeof kpiId !== 'string') {
      throw new ValidationError("Valid KPI ID is required")
    }

    // Update KPI in database
    const { error } = await supabaseAdminQuery
      .employeeKpis()
      .update(data)
      .eq('id', kpiId)

    if (error) {
      console.error("Error updating KPI:", error)
      throw new Error("Failed to update KPI")
    }

    // Log the action
    await logUserAction('kpi:update', { 
      kpiId,
      updatedFields: Object.keys(data)
    })

    // Revalidate cache
    if (data.employee_id) {
      revalidatePath(`/dashboard/employees/${data.employee_id}/profile`)
    }
    
    return { success: true, message: "KPI updated successfully" }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function deleteEmployeeKPI(kpiId: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'kpi-delete', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('employee:write')

    // Validate KPI ID
    if (!kpiId || typeof kpiId !== 'string') {
      throw new ValidationError("Valid KPI ID is required")
    }

    // Get KPI details for logging
    const { data: kpi } = await supabaseAdminQuery
      .employeeKpis()
      .select('employee_id, kpi_name')
      .eq('id', kpiId)
      .single()

    // Delete KPI from database
    const { error } = await supabaseAdminQuery
      .employeeKpis()
      .delete()
      .eq('id', kpiId)

    if (error) {
      console.error("Error deleting KPI:", error)
      throw new Error("Failed to delete KPI")
    }

    // Log the action
    await logUserAction('kpi:delete', { 
      kpiId,
      employeeId: kpi?.employee_id,
      kpiName: kpi?.kpi_name
    })

    // Revalidate cache
    if (kpi?.employee_id) {
      revalidatePath(`/dashboard/employees/${kpi.employee_id}/profile`)
    }
    
    return { success: true, message: "KPI deleted successfully" }
  } catch (error) {
    return handleServerActionError(error)
  }
}