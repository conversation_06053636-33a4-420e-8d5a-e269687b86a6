"use server"

import { getCurrentUser } from "@/lib/auth"
import { handleServerActionError } from "./shared"
import { revalidatePath } from "next/cache"

export interface MarkAsPaidResult {
  success: boolean
  message?: string
  error?: string
}

/**
 * Mark an employee payment as paid
 * Only available to users with 'accountant' role
 */
export async function markAsPaidAction(employeeId: string): Promise<MarkAsPaidResult> {
  try {
    // Get current user and validate permissions
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return {
        success: false,
        error: "Authentication required"
      }
    }

    // Only accountants can mark payments as paid
    if (currentUser.role !== 'accountant') {
      return {
        success: false,
        error: "Only accountants can mark payments as paid"
      }
    }

    // Validate input
    if (!employeeId) {
      return {
        success: false,
        error: "Employee ID is required"
      }
    }

    // TODO: In a real implementation, this would update the database
    // For now, we'll simulate the operation
    console.log(`[ACCOUNTING] Marking payment as paid for employee ${employeeId} by ${currentUser.fullName}`)
    
    // Simulate database update with current timestamp and user info
    const paidAt = new Date().toISOString()
    
    // Note: In a real implementation, you would:
    // 1. Find the accounting record for this employee
    // 2. Update the paymentStatus to "paid"
    // 3. Set isPaid to true
    // 4. Set paidAt to current timestamp
    // 5. Set paidBy to current user ID
    //
    // Example database update:
    // await db.accounting.update({
    //   where: { employeeId },
    //   data: {
    //     paymentStatus: "paid",
    //     isPaid: true,
    //     paidAt,
    //     paidBy: currentUser.id
    //   }
    // })

    // Revalidate the accounting page to show updated data
    revalidatePath('/dashboard/accounting')
    
    return {
      success: true,
      message: "Payment marked as paid successfully"
    }

  } catch (error) {
    console.error('Error marking payment as paid:', error)
    return {
      success: false,
      error: 'Failed to mark payment as paid'
    }
  }
}

/**
 * Get payment history for an employee
 * Available to accountants, hr-admin, and super-admin roles
 */
export async function getPaymentHistoryAction(employeeId: string) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return {
        success: false,
        error: "Authentication required"
      }
    }

    // Check permissions
    if (!['accountant', 'hr-admin', 'super-admin'].includes(currentUser.role)) {
      return {
        success: false,
        error: "Insufficient permissions to view payment history"
      }
    }

    // TODO: In a real implementation, fetch payment history from database
    // For now, return empty history
    const paymentHistory: Array<{
      id: string
      employeeId: string
      paymentDate: string
      amount: number
      paidBy: string
      status: string
    }> = []

    return {
      success: true,
      data: paymentHistory
    }

  } catch (error) {
    console.error('Error fetching payment history:', error)
    return {
      success: false,
      error: 'Failed to fetch payment history'
    }
  }
}

/**
 * Bulk mark multiple payments as paid
 * Only available to users with 'accountant' role
 */
export async function bulkMarkAsPaidAction(employeeIds: string[]): Promise<MarkAsPaidResult> {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return {
        success: false,
        error: "Authentication required"
      }
    }

    // Only accountants can mark payments as paid
    if (currentUser.role !== 'accountant') {
      return {
        success: false,
        error: "Only accountants can mark payments as paid"
      }
    }

    if (!employeeIds.length) {
      return {
        success: false,
        error: "No employees selected"
      }
    }

    // Process each payment
    const results = await Promise.allSettled(
      employeeIds.map(employeeId => markAsPaidAction(employeeId))
    )

    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length
    const failed = results.length - successful

    if (failed > 0) {
      return {
        success: false,
        error: `${failed} payment(s) failed to process. ${successful} payment(s) processed successfully.`
      }
    }

    // Revalidate the accounting page
    revalidatePath('/dashboard/accounting')

    return {
      success: true,
      message: `Successfully marked ${successful} payment(s) as paid`
    }

  } catch (error) {
    console.error('Error in bulk mark as paid:', error)
    return {
      success: false,
      error: 'Failed to process bulk payment marking'
    }
  }
}