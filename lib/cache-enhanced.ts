/**
 * Enhanced caching system with different TTLs for different data types
 * and smart invalidation patterns
 */

export const CACHE_DURATIONS = {
  // Stable data - cache longer
  EMPLOYEE_DATA: 30 * 60 * 1000,      // 30 minutes - employees don't change often
  MANAGER_ROLES: 60 * 60 * 1000,      // 1 hour - very stable
  DEPARTMENTS: 60 * 60 * 1000,        // 1 hour - very stable
  PERIODS: 24 * 60 * 60 * 1000,       // 24 hours - seasonal changes
  MANAGERS: 30 * 60 * 1000,           // 30 minutes - fairly stable
  
  // Dynamic data - cache shorter
  PERFORMANCE_STATS: 2 * 60 * 1000,   // 2 minutes - changes as appraisals submitted
  APPRAISALS: 1 * 60 * 1000,          // 1 minute - frequent updates during review periods
  DASHBOARD_DATA: 3 * 60 * 1000,      // 3 minutes - composite data
  
  // Very dynamic data
  PTO_REQUESTS: 30 * 1000,            // 30 seconds - real-time updates needed
} as const

interface CacheEntry {
  data: any
  expires: number
  type: string
  created: number
}

export class EnhancedCache {
  private cache = new Map<string, CacheEntry>()
  private hitCount = 0
  private missCount = 0

  /**
   * Set cache entry with specific duration and type
   */
  set(key: string, data: any, duration: number, type: string): void {
    this.cache.set(key, {
      data,
      expires: Date.now() + duration,
      type,
      created: Date.now()
    })
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`📦 [CACHE SET] ${key} (${type}) - TTL: ${Math.round(duration / 1000)}s`)
    }
  }

  /**
   * Get cache entry if not expired
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.missCount++
      if (process.env.NODE_ENV === 'development') {
        console.log(`❌ [CACHE MISS] ${key} - Hit rate: ${this.getHitRate()}%`)
      }
      return null
    }

    if (Date.now() > entry.expires) {
      this.cache.delete(key)
      this.missCount++
      if (process.env.NODE_ENV === 'development') {
        console.log(`⏰ [CACHE EXPIRED] ${key} - Hit rate: ${this.getHitRate()}%`)
      }
      return null
    }

    this.hitCount++
    if (process.env.NODE_ENV === 'development') {
      const ageSeconds = Math.round((Date.now() - entry.created) / 1000)
      console.log(`🎯 [CACHE HIT] ${key} (${entry.type}) - Age: ${ageSeconds}s - Hit rate: ${this.getHitRate()}%`)
    }
    
    return entry.data
  }

  /**
   * Remove specific cache entry
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted && process.env.NODE_ENV === 'development') {
      console.log(`🗑️ [CACHE DELETE] ${key}`)
    }
    return deleted
  }

  /**
   * Invalidate all cache entries of a specific type
   */
  invalidateByType(type: string): number {
    let count = 0
    for (const [key, entry] of this.cache) {
      if (entry.type === type) {
        this.cache.delete(key)
        count++
      }
    }
    
    if (process.env.NODE_ENV === 'development' && count > 0) {
      console.log(`🧹 [CACHE INVALIDATE] Cleared ${count} entries of type: ${type}`)
    }
    
    return count
  }

  /**
   * Invalidate cache entries matching patterns
   */
  invalidateByPattern(patterns: string[]): number {
    let count = 0
    for (const [key] of this.cache) {
      for (const pattern of patterns) {
        if (this.matchesPattern(key, pattern)) {
          this.cache.delete(key)
          count++
          break
        }
      }
    }
    
    if (process.env.NODE_ENV === 'development' && count > 0) {
      console.log(`🧹 [CACHE INVALIDATE] Cleared ${count} entries matching patterns:`, patterns)
    }
    
    return count
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const size = this.cache.size
    this.cache.clear()
    this.hitCount = 0
    this.missCount = 0
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🧹 [CACHE CLEAR] Cleared ${size} entries`)
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: this.getHitRate(),
      entries: Array.from(this.cache.entries()).map(([key, entry]) => ({
        key,
        type: entry.type,
        ageSeconds: Math.round((Date.now() - entry.created) / 1000),
        expiresInSeconds: Math.round((entry.expires - Date.now()) / 1000)
      }))
    }
  }

  /**
   * Get cache hit rate percentage
   */
  private getHitRate(): number {
    const total = this.hitCount + this.missCount
    return total > 0 ? Math.round((this.hitCount / total) * 100) : 0
  }

  /**
   * Check if key matches pattern (supports wildcards)
   */
  private matchesPattern(key: string, pattern: string): boolean {
    if (pattern.includes('*')) {
      const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$')
      return regex.test(key)
    }
    return key === pattern
  }

  /**
   * Cleanup expired entries (run periodically)
   */
  cleanup(): number {
    let cleaned = 0
    const now = Date.now()
    
    for (const [key, entry] of this.cache) {
      if (now > entry.expires) {
        this.cache.delete(key)
        cleaned++
      }
    }
    
    if (process.env.NODE_ENV === 'development' && cleaned > 0) {
      console.log(`🧹 [CACHE CLEANUP] Removed ${cleaned} expired entries`)
    }
    
    return cleaned
  }
}

// Global enhanced cache instance
export const enhancedCache = new EnhancedCache()

// Auto-cleanup expired entries every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    enhancedCache.cleanup()
  }, 5 * 60 * 1000)
}

// Cache event manager for smart invalidation
export class CacheEventManager {
  /**
   * Called when employee data is updated
   */
  static onEmployeeUpdate(employeeId: string) {
    enhancedCache.invalidateByPattern([
      `employee-${employeeId}`,
      'all-employees',
      'manager-employees-*',
      'performance-stats-*',
      'dashboard-*'
    ])
  }

  /**
   * Called when manager role is updated
   */
  static onManagerRoleUpdate(userId: string) {
    enhancedCache.invalidateByType('manager-roles')
    enhancedCache.invalidateByPattern([
      `employee-${userId}`,
      'manager-employees-*',
      'dashboard-*'
    ])
  }

  /**
   * Called when appraisal is submitted or updated
   */
  static onAppraisalUpdate(employeeId: string) {
    enhancedCache.invalidateByType('performance-stats')
    enhancedCache.invalidateByPattern([
      'manager-appraisals-*',
      'dashboard-*',
      `appraisal-${employeeId}`
    ])
  }

  /**
   * Called when department data changes
   */
  static onDepartmentUpdate() {
    enhancedCache.invalidateByType('departments')
    enhancedCache.invalidateByPattern([
      'all-employees',
      'manager-employees-*',
      'dashboard-*'
    ])
  }

  /**
   * Called when period data changes
   */
  static onPeriodUpdate() {
    enhancedCache.invalidateByType('periods')
    enhancedCache.invalidateByType('performance-stats')
    enhancedCache.invalidateByPattern([
      'dashboard-*',
      'manager-appraisals-*'
    ])
  }
}

export default enhancedCache