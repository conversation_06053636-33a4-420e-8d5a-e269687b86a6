import { SignUp } from '@clerk/nextjs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function SignUpPage() {
  return (
    <div className="min-h-screen min-h-[100svh] flex items-center justify-center p-3 sm:p-4 bg-muted/30">
      <div className="w-full max-w-sm sm:max-w-md space-y-4 sm:space-y-6">
        <Card className="shadow-sm">
          <CardHeader className="text-center space-y-2 pb-4">
            <CardTitle className="text-xl sm:text-2xl">Create Account</CardTitle>
            <CardDescription className="text-sm sm:text-base px-2">
              Join the Employee Appraisal System
            </CardDescription>
          </CardHeader>
          <CardContent className="px-4 sm:px-6">
            <SignUp 
              appearance={{
                elements: {
                  formButtonPrimary: 'bg-primary hover:bg-primary/90 text-sm sm:text-base py-2 sm:py-3',
                  card: 'shadow-none border-0 p-0',
                  headerTitle: 'hidden',
                  headerSubtitle: 'hidden',
                  socialButtonsBlockButton: 'text-sm sm:text-base py-2 sm:py-3',
                  formFieldInput: 'text-sm sm:text-base py-2 sm:py-3',
                  footerActionLink: 'text-sm',
                  identityPreviewText: 'text-sm',
                  formFieldLabel: 'text-sm font-medium',
                  dividerText: 'text-xs sm:text-sm',
                  otpCodeFieldInput: 'text-base sm:text-lg w-10 h-10 sm:w-12 sm:h-12',
                  formResendCodeLink: 'text-sm',
                  alertText: 'text-sm',
                  formFieldSuccessText: 'text-sm',
                  formFieldErrorText: 'text-sm',
                  formFieldHintText: 'text-xs sm:text-sm',
                  formFieldAction: 'text-sm',
                  menuButton: 'text-sm',
                  selectButtonIcon: 'w-4 h-4'
                },
                layout: {
                  socialButtonsPlacement: 'bottom',
                  socialButtonsVariant: 'blockButton'
                }
              }}
              routing="path"
              path="/sign-up"
              afterSignUpUrl="/dashboard"
            />
          </CardContent>
        </Card>
        
        <div className="text-center text-xs sm:text-sm text-muted-foreground px-4">
          <p>By signing up, you agree to our terms of service and privacy policy.</p>
        </div>
      </div>
    </div>
  )
}
