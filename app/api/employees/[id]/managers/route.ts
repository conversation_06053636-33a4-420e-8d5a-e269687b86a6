import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { db } from '@/lib/db'
import { ensureEmployeeManagerSync } from '@/lib/utils/manager-sync'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('🔧 [API] GET /api/employees/[id]/managers - Starting request')
    
    const { id: employeeId } = await params
    console.log('🔧 [API] Employee ID:', employeeId)

    // Check authentication
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      console.log('🔧 [API] No authenticated user found')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('🔧 [API] Current user:', currentUser.fullName, 'Role:', currentUser.role)

    // Check permissions - only admins or the employee themselves can view manager relationships
    const isAdmin = currentUser.role === 'hr-admin' || currentUser.role === 'super-admin'
    const isOwnProfile = currentUser.id === employeeId
    
    if (!isAdmin && !isOwnProfile) {
      console.log('🔧 [API] Access denied - not admin and not own profile')
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Ensure manager data is synced from legacy system
    console.log('🔧 [API] Ensuring manager sync for employee:', employeeId)
    await ensureEmployeeManagerSync(employeeId)

    // Get existing manager relationships
    console.log('🔧 [API] Fetching manager relationships for employee:', employeeId)
    const managers = await db.getEmployeeManagers(employeeId)

    console.log('🔧 [API] Found', managers.length, 'manager relationships:', managers)

    return NextResponse.json(managers)
  } catch (error) {
    console.error('🔧 [API] Error fetching employee managers:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
