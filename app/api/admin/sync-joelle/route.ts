import { NextResponse } from 'next/server'
import { clerkClient } from '@clerk/nextjs/server'
import { supabaseAdmin } from '@/lib/supabase-admin'

export async function POST() {
  try {
    console.log('🔄 [ADMIN] Starting <PERSON>le Clerk ID sync...')

    // Step 1: Get <PERSON><PERSON>'s current database record
    const { data: joelleDb, error: dbError } = await supabaseAdmin
      .from('appy_managers')
      .select('*')
      .eq('email', '<EMAIL>')
      .single()

    if (dbError || !joelleDb) {
      console.error('❌ [ADMIN] Could not find <PERSON><PERSON> in database:', dbError?.message)
      return NextResponse.json({
        success: false,
        error: '<PERSON><PERSON> not found in database',
        details: dbError?.message
      }, { status: 404 })
    }

    console.log('✅ [ADMIN] Found <PERSON><PERSON> in database:', {
      current_clerk_id: joelleDb.user_id,
      role: joelleDb.role,
      active: joelleDb.active
    })

    // Step 2: Get <PERSON><PERSON>'s actual Clerk ID from Clerk
    const clerkUsers = await clerkClient.users.getUserList({
      emailAddress: ['<EMAIL>']
    })

    if (!clerkUsers.data || clerkUsers.data.length === 0) {
      console.error('❌ [ADMIN] Could not find Joelle in Clerk')
      return NextResponse.json({
        success: false,
        error: 'Joelle not found in Clerk with email: <EMAIL>',
        suggestion: 'Make sure Joelle has signed up with this exact email address'
      }, { status: 404 })
    }

    const joelleClerk = clerkUsers.data[0]
    console.log('✅ [ADMIN] Found Joelle in Clerk:', {
      clerk_id: joelleClerk.id,
      email: joelleClerk.emailAddresses[0]?.emailAddress,
      name: `${joelleClerk.firstName} ${joelleClerk.lastName}`.trim()
    })

    // Step 3: Update database with correct information
    const updateData = {
      user_id: joelleClerk.id,
      role: 'hr-admin' as const,
      active: true,
      full_name: `${joelleClerk.firstName} ${joelleClerk.lastName}`.trim()
    }

    const { data: updateResult, error: updateError } = await supabaseAdmin
      .from('appy_managers')
      .update(updateData)
      .eq('email', '<EMAIL>')
      .select()
      .single()

    if (updateError) {
      console.error('❌ [ADMIN] Failed to update Joelle\'s record:', updateError.message)
      return NextResponse.json({
        success: false,
        error: 'Failed to update database',
        details: updateError.message
      }, { status: 500 })
    }

    console.log('✅ [ADMIN] Successfully updated Joelle\'s record')

    // Step 4: Verify the update
    const wasClerkIdChanged = joelleDb.user_id !== joelleClerk.id
    const wasRoleFixed = joelleDb.role !== 'hr-admin'
    const wasActivated = !joelleDb.active

    return NextResponse.json({
      success: true,
      message: 'Joelle\'s account has been successfully synced',
      changes: {
        clerkIdUpdated: wasClerkIdChanged,
        roleFixed: wasRoleFixed,
        accountActivated: wasActivated
      },
      before: {
        clerk_id: joelleDb.user_id,
        role: joelleDb.role,
        active: joelleDb.active,
        full_name: joelleDb.full_name
      },
      after: updateResult,
      nextSteps: [
        'Have Joelle refresh her browser/clear cache',
        'She should now see the "Add New Employee" button',
        'She can access /dashboard/add-employee',
        'Test by trying to add a new employee'
      ]
    })

  } catch (error) {
    console.error('🚨 [ADMIN] Sync failed:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    // Just check current status without making changes
    const { data: joelleDb } = await supabaseAdmin
      .from('appy_managers')
      .select('*')
      .eq('email', '<EMAIL>')
      .single()

    const clerkUsers = await clerkClient.users.getUserList({
      emailAddress: ['<EMAIL>']
    })

    const joelleClerk = clerkUsers.data?.[0]

    return NextResponse.json({
      database: joelleDb,
      clerk: joelleClerk ? {
        id: joelleClerk.id,
        email: joelleClerk.emailAddresses[0]?.emailAddress,
        name: `${joelleClerk.firstName} ${joelleClerk.lastName}`.trim(),
        lastSignIn: joelleClerk.lastSignInAt
      } : null,
      clerkIdMatch: joelleDb?.user_id === joelleClerk?.id,
      needsSync: joelleDb?.user_id !== joelleClerk?.id || joelleDb?.role !== 'hr-admin' || !joelleDb?.active
    })

  } catch (error) {
    console.error('🚨 [ADMIN] Status check failed:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
