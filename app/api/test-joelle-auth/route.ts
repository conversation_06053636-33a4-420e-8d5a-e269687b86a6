import { NextResponse } from 'next/server'
import { getCurrentUser, hasPermission, forceSyncUser } from '@/lib/auth'
import { supabaseAdmin } from '@/lib/supabase-admin'
import { auth, currentUser } from '@clerk/nextjs/server'

export async function GET() {
  try {
    console.log('🔍 [JOELLE TEST] Starting Joelle authentication test')
    
    // Get current user through our auth system
    const user = await getCurrentUser()
    
    // Get Clerk details
    const { userId: clerkUserId } = await auth()
    const clerkUser = await currentUser()
    
    console.log('🔍 [JOELLE TEST] Clerk user details:', {
      clerkUserId,
      email: clerkUser?.emailAddresses[0]?.emailAddress,
      firstName: clerkUser?.firstName,
      lastName: clerkUser?.lastName,
      fullName: `${clerkUser?.firstName} ${clerkUser?.lastName}`.trim()
    })

    // Check database for <PERSON><PERSON>'s record
    const { data: joelle<PERSON><PERSON>ord, error: joelle<PERSON><PERSON>r } = await supabaseAdmin
      .from('appy_managers')
      .select('*')
      .eq('email', '<EMAIL>')
      .single()

    console.log('🔍 [JOELLE TEST] Database record for Joelle:', joelleRecord)
    
    // Check if current user is Joelle
    const isJoelle = clerkUser?.emailAddresses[0]?.emailAddress === '<EMAIL>'
    
    let authResult = {
      isJoelle,
      clerkDetails: {
        userId: clerkUserId,
        email: clerkUser?.emailAddresses[0]?.emailAddress,
        fullName: `${clerkUser?.firstName} ${clerkUser?.lastName}`.trim()
      },
      databaseRecord: joelleRecord,
      currentUser: user,
      clerkIdMatch: joelleRecord?.user_id === clerkUserId,
      permissions: user ? {
        role: user.role,
        canWriteEmployees: hasPermission(user.role, 'employee:write'),
        canReadEmployees: hasPermission(user.role, 'employee:read'),
        isHRAdmin: user.role === 'hr-admin'
      } : null
    }

    // If this is Joelle and there's a mismatch, try to sync
    if (isJoelle && joelleRecord && joelleRecord.user_id !== clerkUserId) {
      console.log('🔄 [JOELLE TEST] Clerk ID mismatch detected, attempting sync...')
      
      // Update Joelle's Clerk ID in the database
      const { data: updateResult, error: updateError } = await supabaseAdmin
        .from('appy_managers')
        .update({ user_id: clerkUserId })
        .eq('email', '<EMAIL>')
        .select()
        .single()

      if (updateError) {
        console.error('🚨 [JOELLE TEST] Failed to update Clerk ID:', updateError)
        authResult = {
          ...authResult,
          syncAttempt: {
            success: false,
            error: updateError.message
          }
        }
      } else {
        console.log('✅ [JOELLE TEST] Successfully updated Clerk ID')
        authResult = {
          ...authResult,
          syncAttempt: {
            success: true,
            oldClerkId: joelleRecord.user_id,
            newClerkId: clerkUserId,
            updatedRecord: updateResult
          }
        }
      }
    }

    return NextResponse.json(authResult)
    
  } catch (error) {
    console.error('🚨 [JOELLE TEST] Error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}

export async function POST() {
  try {
    console.log('🔄 [JOELLE TEST] Manual sync requested')
    
    // Force sync Joelle's account
    const syncResult = await forceSyncUser('<EMAIL>')
    
    return NextResponse.json({
      message: 'Manual sync completed',
      result: syncResult
    })
    
  } catch (error) {
    console.error('🚨 [JOELLE TEST] Manual sync error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
