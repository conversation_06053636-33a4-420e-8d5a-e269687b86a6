import { NextResponse } from 'next/server'
import { getCurrentUser, hasPermission } from '@/lib/auth'
import { getDepartments } from '@/lib/data/departments'
import { getManagers } from '@/lib/data/managers'

export async function GET() {
  try {
    console.log('🧪 [ADD EMPLOYEE TEST] Testing add employee page access')
    
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json({ 
        canAccess: false,
        reason: 'Not authenticated',
        user: null
      })
    }

    // Check if user has permission to add employees (same logic as the page)
    const canAddEmployees = ['super-admin', 'hr-admin', 'admin'].includes(user.role)
    const hasWritePermission = hasPermission(user.role, 'employee:write')
    
    console.log('🧪 [ADD EMPLOYEE TEST] Access check:', {
      userRole: user.role,
      canAddEmployees,
      hasWritePermission,
      allowedRoles: ['super-admin', 'hr-admin', 'admin']
    })

    let testResults = {
      user: {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        role: user.role
      },
      access: {
        canAddEmployees,
        hasWritePermission,
        wouldRedirect: !canAddEmployees
      },
      permissions: {
        'employee:read': hasPermission(user.role, 'employee:read'),
        'employee:write': hasPermission(user.role, 'employee:write'),
        'employee:delete': hasPermission(user.role, 'employee:delete'),
        'department:read': hasPermission(user.role, 'department:read'),
        'department:write': hasPermission(user.role, 'department:write')
      }
    }

    // If user can access, test loading required data
    if (canAddEmployees) {
      try {
        console.log('🧪 [ADD EMPLOYEE TEST] Testing data loading...')
        
        const [departments, managers] = await Promise.all([
          getDepartments(),
          getManagers()
        ])

        testResults = {
          ...testResults,
          dataLoading: {
            success: true,
            departments: {
              count: departments.length,
              sample: departments.slice(0, 3).map(d => ({ id: d.id, name: d.name }))
            },
            managers: {
              count: managers.length,
              sample: managers.slice(0, 3).map(m => ({ id: m.id, fullName: m.fullName, role: m.role }))
            }
          }
        }
        
        console.log('✅ [ADD EMPLOYEE TEST] Data loading successful')
        
      } catch (dataError) {
        console.error('❌ [ADD EMPLOYEE TEST] Data loading failed:', dataError)
        testResults = {
          ...testResults,
          dataLoading: {
            success: false,
            error: dataError instanceof Error ? dataError.message : 'Unknown error'
          }
        }
      }
    }

    return NextResponse.json(testResults)
    
  } catch (error) {
    console.error('🚨 [ADD EMPLOYEE TEST] Error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}
