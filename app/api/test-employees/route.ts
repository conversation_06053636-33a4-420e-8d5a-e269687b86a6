import { NextResponse } from 'next/server'
import { getEmployees, getEmployeesForManager } from '@/lib/data/index'
import { getCurrentUser } from '@/lib/auth'

export async function GET() {
  try {
    // Get current user
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }
    
    console.log('🧪 [TEST] Current user:', {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      role: user.role
    })
    
    // Test getEmployees
    console.log('🧪 [TEST] Testing getEmployees()...')
    const allEmployees = await getEmployees()
    console.log('🧪 [TEST] getEmployees returned:', allEmployees.length, 'employees')
    
    // Test getEmployeesForManager
    console.log('🧪 [TEST] Testing getEmployeesForManager()...')
    const managerEmployees = await getEmployeesFor<PERSON>anager(user.id, user.role)
    console.log('🧪 [TEST] getEmployeesForManager returned:', managerEmployees.length, 'employees')
    
    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        role: user.role
      },
      allEmployees: {
        count: allEmployees.length,
        sample: allEmployees.slice(0, 3)
      },
      managerEmployees: {
        count: managerEmployees.length,
        sample: managerEmployees.slice(0, 3)
      }
    })
  } catch (error) {
    console.error('🧪 [TEST] Error:', error)
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}