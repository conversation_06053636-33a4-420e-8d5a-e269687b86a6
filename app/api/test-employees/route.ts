import { NextResponse } from 'next/server'
import { getEmployees, getEmployeesForManager } from '@/lib/data/index'
import { getCurrentUser, hasPermission } from '@/lib/auth'
import { supabaseAdmin } from '@/lib/supabase-admin'
import { auth, currentUser } from '@clerk/nextjs/server'

export async function GET() {
  try {
    // Get current user
    const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    console.log('🧪 [TEST] Current user:', {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      role: user.role
    })

    // Test Clerk authentication details
    const { userId: clerkUserId } = await auth()
    const clerkUser = await currentUser()

    console.log('🧪 [TEST] Clerk details:', {
      clerkUserId,
      clerkEmail: clerkUser?.emailAddresses[0]?.emailAddress,
      clerkFullName: `${clerkUser?.firstName} ${clerkUser?.lastName}`.trim()
    })

    // Check database lookup for this user
    const { data: dbUser, error: dbError } = await supabaseAdmin
      .from('appy_managers')
      .select('user_id, full_name, email, role, active')
      .eq('user_id', clerkUserId)
      .single()

    console.log('🧪 [TEST] Database lookup:', {
      clerkUserId,
      dbUser,
      dbError: dbError?.message
    })

    // Test permissions
    const canWriteEmployees = hasPermission(user.role, 'employee:write')
    const canReadEmployees = hasPermission(user.role, 'employee:read')

    console.log('🧪 [TEST] Permissions:', {
      role: user.role,
      canWriteEmployees,
      canReadEmployees
    })

    // Special check for Joelle
    if (user.email === '<EMAIL>') {
      console.log('🔍 [JOELLE TEST] Special debugging for Joelle')

      // Check if Joelle's Clerk ID matches database
      const { data: joelleDb } = await supabaseAdmin
        .from('appy_managers')
        .select('*')
        .eq('email', '<EMAIL>')
        .single()

      console.log('🔍 [JOELLE TEST] Database record:', joelleDb)
      console.log('🔍 [JOELLE TEST] Clerk ID match:', joelleDb?.user_id === clerkUserId)
    }

    // Test getEmployees
    console.log('🧪 [TEST] Testing getEmployees()...')
    const allEmployees = await getEmployees()
    console.log('🧪 [TEST] getEmployees returned:', allEmployees.length, 'employees')

    // Test getEmployeesForManager
    console.log('🧪 [TEST] Testing getEmployeesForManager()...')
    const managerEmployees = await getEmployeesForManager(user.id, user.role)
    console.log('🧪 [TEST] getEmployeesForManager returned:', managerEmployees.length, 'employees')

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        role: user.role
      },
      clerkDetails: {
        clerkUserId,
        clerkEmail: clerkUser?.emailAddresses[0]?.emailAddress,
        clerkFullName: `${clerkUser?.firstName} ${clerkUser?.lastName}`.trim()
      },
      databaseLookup: {
        found: !!dbUser,
        user: dbUser,
        error: dbError?.message
      },
      permissions: {
        canWriteEmployees,
        canReadEmployees
      },
      allEmployees: {
        count: allEmployees.length,
        sample: allEmployees.slice(0, 3)
      },
      managerEmployees: {
        count: managerEmployees.length,
        sample: managerEmployees.slice(0, 3)
      }
    })
  } catch (error) {
    console.error('🧪 [TEST] Error:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}