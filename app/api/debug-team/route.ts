import { getCurrentUser } from '@/lib/auth'
import { getEmployeesForManager } from '@/lib/data/employees'
import { supabase } from '@/lib/supabase'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    console.log('🔍 [DEBUG TEAM API] Starting debug trace')
    
    // Get current user
    const currentUser = await getCurrentUser()
    console.log('🔍 [DEBUG TEAM API] Current user:', {
      id: currentUser?.id,
      fullName: currentUser?.fullName,
      role: currentUser?.role,
      email: currentUser?.email
    })

    if (!currentUser) {
      return NextResponse.json({ error: 'No user found' }, { status: 401 })
    }

    // Special focus on Mona
    const isMona = currentUser.email === '<EMAIL>' || currentUser.fullName?.includes('Mona')
    
    if (isMona) {
      console.log('🎯 [MONA DEBUG] Found Mona, starting detailed trace')
    }

    // Step 1: Test the SQL function directly
    console.log('🔍 [DEBUG TEAM API] Step 1: Testing SQL function directly')
    const { data: sqlData, error: sqlError } = await supabase.rpc('get_hierarchical_employees', {
      manager_user_id: currentUser.id
    })

    console.log('🔍 [DEBUG TEAM API] SQL function result:', {
      dataCount: sqlData?.length || 0,
      error: sqlError?.message,
      sampleData: sqlData?.[0]
    })

    if (isMona) {
      console.log('🎯 [MONA DEBUG] SQL function returned:', {
        count: sqlData?.length || 0,
        allEmployees: sqlData?.map((emp: any) => ({
          id: emp.id,
          name: emp.full_name,
          department: emp.department_name,
          manager: emp.manager_name,
          hierarchyLevel: emp.hierarchy_level
        }))
      })
    }

    // Step 2: Test getEmployeesForManager function
    console.log('🔍 [DEBUG TEAM API] Step 2: Testing getEmployeesForManager function')
    const managedEmployees = await getEmployeesForManager(currentUser.id, currentUser.role)
    
    console.log('🔍 [DEBUG TEAM API] getEmployeesForManager result:', {
      count: managedEmployees.length,
      sampleEmployee: managedEmployees[0] ? {
        id: managedEmployees[0].id,
        fullName: managedEmployees[0].fullName,
        departmentName: managedEmployees[0].departmentName,
        managerId: managedEmployees[0].managerId,
        hierarchyLevel: managedEmployees[0].hierarchyLevel
      } : null
    })

    if (isMona) {
      console.log('🎯 [MONA DEBUG] getEmployeesForManager function returned:', {
        count: managedEmployees.length,
        allEmployees: managedEmployees.map(emp => ({
          id: emp.id,
          name: emp.fullName,
          department: emp.departmentName,
          manager: emp.managerName,
          hierarchyLevel: emp.hierarchyLevel
        }))
      })
    }

    // Step 3: Check the team page transformation
    const employeesByDepartment = managedEmployees.reduce((acc, employee) => {
      const deptName = employee.departmentName || 'Unknown Department'
      if (!acc[deptName]) {
        acc[deptName] = []
      }
      acc[deptName].push(employee)
      return acc
    }, {} as Record<string, typeof managedEmployees>)

    console.log('🔍 [DEBUG TEAM API] Department grouping result:', {
      departmentCount: Object.keys(employeesByDepartment).length,
      departments: Object.entries(employeesByDepartment).map(([dept, emps]) => ({
        department: dept,
        employeeCount: emps.length
      }))
    })

    // Step 4: Authentication check
    console.log('🔍 [DEBUG TEAM API] Step 4: Checking authentication context')
    const authCheck = {
      hasUserId: !!currentUser.id,
      userIdLength: currentUser.id?.length,
      isManagerOrSeniorManager: ['manager', 'senior-manager'].includes(currentUser.role),
      roleCheck: currentUser.role
    }

    console.log('🔍 [DEBUG TEAM API] Auth check result:', authCheck)

    return NextResponse.json({
      success: true,
      debug: {
        currentUser: {
          id: currentUser.id,
          fullName: currentUser.fullName,
          role: currentUser.role,
          email: currentUser.email
        },
        sqlFunction: {
          count: sqlData?.length || 0,
          error: sqlError?.message || null,
          sampleData: sqlData?.[0] || null
        },
        managedEmployees: {
          count: managedEmployees.length,
          sampleEmployee: managedEmployees[0] || null
        },
        departmentGrouping: {
          departmentCount: Object.keys(employeesByDepartment).length,
          departments: Object.keys(employeesByDepartment)
        },
        authCheck,
        isMona
      }
    })

  } catch (error) {
    console.error('🚨 [DEBUG TEAM API] Error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}