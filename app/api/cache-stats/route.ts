import { NextRequest, NextResponse } from 'next/server'
import { enhancedCache } from '@/lib/cache-enhanced'

export async function GET(request: NextRequest) {
  try {
    const stats = enhancedCache.getStats()
    
    return NextResponse.json({
      success: true,
      stats: {
        ...stats,
        summary: {
          hitRate: `${stats.hitRate}%`,
          totalRequests: stats.hitCount + stats.missCount,
          cacheSize: stats.size,
          entryTypes: stats.entries.reduce((acc, entry) => {
            acc[entry.type] = (acc[entry.type] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        }
      }
    })
  } catch (error) {
    console.error('Error getting cache stats:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get cache stats' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    enhancedCache.clear()
    
    return NextResponse.json({
      success: true,
      message: 'Cache cleared successfully'
    })
  } catch (error) {
    console.error('Error clearing cache:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to clear cache' },
      { status: 500 }
    )
  }
}