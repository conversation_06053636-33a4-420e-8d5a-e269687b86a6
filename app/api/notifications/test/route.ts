import { NextRequest, NextResponse } from 'next/server'
import { notificationService } from '@/lib/services/notifications'
import { emailService } from '@/lib/services/email'
import { getCurrentUser } from '@/lib/auth'
import { supabaseAdmin } from '@/lib/supabase-admin'

/**
 * POST - Send test notification to current user
 */
export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get manager details
    const { data: manager, error: managerError } = await supabaseAdmin
      .from('appy_managers')
      .select('user_id, full_name, email')
      .eq('user_id', currentUser.id)
      .single()

    if (managerError || !manager) {
      return NextResponse.json({ 
        error: 'Manager profile not found. Only managers can receive appraisal reminders.' 
      }, { status: 400 })
    }

    // Get current period
    const { data: currentPeriod, error: periodError } = await supabaseAdmin
      .from('appy_appraisal_periods')
      .select('*')
      .eq('closed', false)
      .order('period_end', { ascending: true })
      .limit(1)
      .single()

    if (periodError || !currentPeriod) {
      return NextResponse.json({ 
        error: 'No active appraisal period found' 
      }, { status: 400 })
    }

    // Create test notification data
    const testNotificationData = {
      user_id: currentUser.id,
      manager_name: manager.full_name,
      manager_email: manager.email,
      period_end: currentPeriod.period_end,
      pending_appraisals: [
        {
          employee_id: 'test-employee-1',
          employee_name: 'John Doe',
          department_name: 'Engineering',
          days_remaining: 4
        },
        {
          employee_id: 'test-employee-2',
          employee_name: 'Jane Smith',
          department_name: 'Marketing',
          days_remaining: 4
        }
      ]
    }

    // Send test email
    const emailSent = await emailService.sendAppraisalReminder(testNotificationData)

    if (emailSent) {
      return NextResponse.json({ 
        success: true, 
        message: `Test notification sent to ${manager.email}` 
      })
    } else {
      return NextResponse.json({ 
        error: 'Failed to send test notification' 
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error sending test notification:', error)
    return NextResponse.json({ 
      error: 'Failed to send test notification' 
    }, { status: 500 })
  }
}
