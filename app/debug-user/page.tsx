import { getCurrentUser } from '@/lib/auth'
import { supabase } from '@/lib/supabase'
import { redirect } from 'next/navigation'

export default async function DebugUserPage() {
  // Check if this page should be accessible in production
  if (process.env.NODE_ENV === 'production') {
    redirect('/dashboard')
  }

  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/sign-in')
  }

  // Check if user is super-admin
  if (user.role !== 'super-admin') {
    redirect('/dashboard?error=access_denied')
  }

  // Get all super-admin users from database with error handling
  const { data: superAdmins, error } = await supabase
    .from('appy_managers')
    .select(`
      user_id,
      role,
      full_name,
      email
    `)
    .eq('role', 'super-admin')

  if (error) {
    console.error('Error fetching super-admin users:', error)
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Error</h1>
        <p className="text-red-600">Failed to fetch super-admin users</p>
      </div>
    )
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">User Debug Information</h1>
      
      <div className="bg-gray-100 p-4 rounded mb-4">
        <h2 className="text-lg font-semibold mb-2">Current User (from getCurrentUser)</h2>
        <pre className="text-sm">{JSON.stringify(user, null, 2)}</pre>
      </div>

      <div className="bg-blue-100 p-4 rounded mb-4">
        <h2 className="text-lg font-semibold mb-2">All Super-Admin Users in Database</h2>
        <pre className="text-sm">{JSON.stringify(superAdmins, null, 2)}</pre>
      </div>

      <div className="bg-yellow-100 p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Troubleshooting</h2>
        <ul className="list-disc list-inside text-sm">
          <li>Check if your email matches any super-admin email in the database</li>
          <li>Check if your Clerk ID matches any super-admin user_id in the database</li>
          <li>System Admin requires "super-admin" role exactly</li>
        </ul>
      </div>
    </div>
  )
}
