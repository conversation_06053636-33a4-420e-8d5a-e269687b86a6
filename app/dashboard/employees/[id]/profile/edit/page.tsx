import { notFound, redirect } from "next/navigation"
import { EmployeeProfileForm } from "@/components/employee-profile-form"
import { getEmployeeProfile } from "@/lib/data/employees"
import { getDepartments } from "@/lib/data/departments"
import { getManagers } from "@/lib/data/managers"
import { getCurrentUser } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default async function EditEmployeeProfilePage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  const { id } = await params
  const currentUser = await getCurrentUser()
  const employee = await getEmployeeProfile(id)

  if (!employee) {
    notFound()
  }

  // Get departments and managers for admin users
  const isAdmin = currentUser?.role === 'hr-admin' || currentUser?.role === 'super-admin'
  const departments = isAdmin ? await getDepartments() : []
  const managers = isAdmin ? await getManagers() : []

  // Check if current user can edit this profile
  const canEdit = 
    currentUser?.id === id || // Employee can edit their own profile
    currentUser?.role === 'hr-admin' ||
    currentUser?.role === 'super-admin' ||
    (currentUser?.role === 'manager' && employee.managerId === currentUser.id)

  if (!canEdit) {
    redirect(`/dashboard/employees/${id}/profile`)
  }

  return (
    <div className="container max-w-2xl py-8">
      <Card>
        <CardHeader>
          <CardTitle>Edit Profile</CardTitle>
          <CardDescription>
            Update employee profile information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EmployeeProfileForm
            employee={employee}
            departments={departments}
            managers={managers}
            isAdmin={isAdmin}
          />
        </CardContent>
      </Card>
    </div>
  )
}