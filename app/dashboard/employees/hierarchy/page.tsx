import { requirePermission } from "@/lib/auth"
import { getHierarchyData } from "@/lib/data/hierarchy"
import { getDepartments } from "@/lib/data/departments"
import { HierarchyVisualization } from "@/components/hierarchy-visualization"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default async function HierarchyPage() {
  // All authenticated users with employee:read permission can view hierarchy
  await requirePermission('employee:read')
  
  const [hierarchyData, departments] = await Promise.all([
    getHierarchyData(),
    getDepartments()
  ])

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Organizational Hierarchy</h1>
        <p className="text-muted-foreground">
          Visual representation of reporting relationships and department structure
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Company Structure</CardTitle>
          <CardDescription>
            Interactive organization chart showing managers, senior managers, and reporting relationships
          </CardDescription>
        </CardHeader>
        <CardContent>
          <HierarchyVisualization data={hierarchyData} departments={departments} />
        </CardContent>
      </Card>
    </div>
  )
}