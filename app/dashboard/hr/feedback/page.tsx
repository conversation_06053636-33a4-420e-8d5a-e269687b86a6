import { auth } from "@clerk/nextjs/server"
import { db } from "@/lib/db"
import { getPendingFeedbackForHR, getFeedbackStatistics } from "@/lib/data/feedback"
import { HRFeedbackDashboard } from "@/components/hr-feedback-dashboard"
import { redirect } from "next/navigation"
import { FeedbackStatistics, EmployeeFeedback } from "@/lib/types"

export default async function HRFeedbackPage() {
  const { userId } = await auth()

  if (!userId) {
    redirect('/sign-in')
  }

  // Check if user has HR permissions
  const userRole = await db.getUserRole(userId)
  if (!userRole || !['hr-admin', 'super-admin'].includes(userRole)) {
    redirect('/dashboard')
  }

  // Fetch feedback data with error handling
  let pendingFeedback: any[] = []
  let statistics: FeedbackStatistics | null = null

  try {
    console.log('🔄 [HR-FEEDBACK] Fetching feedback data...')
    const results = await Promise.all([
      getPendingFeedbackForHR(),
      getFeedbackStatistics()
    ])
    pendingFeedback = results[0] || []
    statistics = results[1]
    console.log('✅ [HR-FEEDBACK] Feedback data fetched successfully:', {
      pendingCount: pendingFeedback.length,
      statistics
    })
  } catch (error) {
    console.error('❌ [HR-FEEDBACK] Error fetching feedback data:', error)
    // Continue with empty data rather than crashing the page
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex flex-col gap-4">
        <h1 className="text-3xl font-bold">HR Feedback Management</h1>
        <p className="text-muted-foreground">
          Review and manage employee feedback submissions
        </p>
      </div>

      <HRFeedbackDashboard
        pendingFeedback={pendingFeedback}
        statistics={statistics}
      />
    </div>
  )
}
