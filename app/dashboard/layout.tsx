import type React from "react"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { AppSidebar } from "@/components/app-sidebar"
import { SidebarProvider } from "@/components/ui/sidebar"
import { AppHeader } from "@/components/app-header"
import { getCurrentUser } from "@/lib/auth"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertTriangle } from "lucide-react"
import { DevPerformanceMonitor } from "@/components/performance-monitor"


export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Require authentication
  const user = await getCurrentUser()

  if (!user) {
    redirect('/sign-in')
  }

  const cookieStore = await cookies()
  const defaultOpen = cookieStore.get("sidebar:state")?.value !== "false"

  // Convert to the format expected by AppSidebar
  const mockUser = {
    id: user.id,
    fullName: user.fullName,
    role: user.role,
  }


  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      {/* Skip Links */}
      <div className="sr-only focus-within:not-sr-only">
        <a
          href="#main-content"
          className="absolute top-4 left-4 z-50 bg-primary text-primary-foreground px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-ring"
        >
          Skip to main content
        </a>
        <a
          href="#navigation"
          className="absolute top-4 left-32 z-50 bg-primary text-primary-foreground px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-ring"
        >
          Skip to navigation
        </a>
      </div>

      <AppSidebar user={mockUser} />
      <main id="main-content" className="flex-1 bg-muted/40 min-h-screen" role="main">
        <AppHeader />
        <ErrorAlert />
        <div className="w-full max-w-none mobile-container py-4 sm:py-6">
          {children}
        </div>
      </main>


      
      {/* Development performance monitor */}
      <DevPerformanceMonitor />
    </SidebarProvider>
  )
}

// Component to show access denied errors
function ErrorAlert() {
  // This would be populated from URL params in a real implementation
  const error = null // searchParams.get('error')

  if (!error) return null

  const errorMessages = {
    access_denied: 'You do not have permission to access that page.',
    insufficient_permissions: 'Your account does not have sufficient permissions for this action.',
    session_expired: 'Your session has expired. Please sign in again.',
  }

  const message = errorMessages[error as keyof typeof errorMessages] || 'An error occurred.'

  return (
    <div className="p-4">
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{message}</AlertDescription>
      </Alert>
    </div>
  )
}
