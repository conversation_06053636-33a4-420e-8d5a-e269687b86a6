import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/auth'
import { getDepartments } from '@/lib/data/departments'
import { getManagers } from '@/lib/data/managers'
import { AddEmployeeForm } from '@/components/add-employee-form'

export default async function AddEmployeePage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect('/sign-in')
  }

  // Check if user has permission to add employees
  const canAddEmployees = ['super-admin', 'hr-admin', 'admin'].includes(user.role)
  
  if (!canAddEmployees) {
    redirect('/dashboard')
  }

  // Fetch required data
  const [departments, managers] = await Promise.all([
    getDepartments(),
    getManagers()
  ])

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Add New Employee</h1>
        <p className="text-muted-foreground">
          Fill out the form below to add a new employee to the system
        </p>
      </div>

      <AddEmployeeForm 
        departments={departments}
        managers={managers}
        user={user}
      />
    </div>
  )
}
