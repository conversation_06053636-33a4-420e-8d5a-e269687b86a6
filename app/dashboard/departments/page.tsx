import { getDepartments } from "@/lib/data/index"
import { getCurrentUser } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DepartmentsTable } from "@/components/departments-table"
import { RoleGuard } from "@/components/role-guard"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield } from "lucide-react"

export default async function DepartmentsPage() {
  const user = await getCurrentUser()

  if (!user) {
    return (
      <Alert variant="destructive">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Authentication required. Please sign in to access this page.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <RoleGuard
      allowedRoles={['hr-admin', 'super-admin']}
      userRole={user.role}
      redirectTo="/dashboard"
    >
      <DepartmentManagementContent />
    </RoleGuard>
  )
}

async function DepartmentManagementContent() {
  const departments = await getDepartments()

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Department Management</h1>
        <p className="text-sm sm:text-base text-muted-foreground mt-1">Add, edit, and manage all company departments.</p>
      </div>
      <Card>
        <CardHeader>
          <CardTitle className="text-base sm:text-lg">All Departments</CardTitle>
          <CardDescription className="text-sm">A list of all departments in the system.</CardDescription>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <DepartmentsTable data={departments} />
        </CardContent>
      </Card>
    </div>
  )
}
