// Server-side test script for HR admin functionality
// Run this with: npx tsx test-hr-admin.ts

import { supabaseAdmin } from './lib/supabase-admin'

async function testHRAdminSetup() {
  console.log('🧪 Testing HR Admin Setup...\n')

  try {
    // Test 1: Check <PERSON><PERSON>'s database record
    console.log('📋 Test 1: Checking <PERSON><PERSON>\'s database record...')
    const { data: joelleRecord, error: joelle<PERSON>rror } = await supabaseAdmin
      .from('appy_managers')
      .select('*')
      .eq('email', '<EMAIL>')
      .single()

    if (joelleError) {
      console.error('❌ Error fetching <PERSON><PERSON>:', joelleError.message)
      return
    }

    if (!joelleRecord) {
      console.error('❌ <PERSON>le not found in database')
      return
    }

    console.log('✅ <PERSON><PERSON> found:', {
      user_id: joelleRecord.user_id,
      full_name: joelle<PERSON><PERSON>ord.full_name,
      email: joelleRecord.email,
      role: joelle<PERSON><PERSON>ord.role,
      active: joelleRecord.active
    })

    // Test 2: Check if role is correct
    if (joelleRecord.role !== 'hr-admin') {
      console.error('❌ <PERSON>le does not have hr-admin role:', joelleRecord.role)
      
      // Fix the role
      console.log('🔧 Fixing Joelle\'s role...')
      const { error: updateError } = await supabaseAdmin
        .from('appy_managers')
        .update({ role: 'hr-admin' })
        .eq('email', '<EMAIL>')

      if (updateError) {
        console.error('❌ Failed to update role:', updateError.message)
      } else {
        console.log('✅ Role updated to hr-admin')
      }
    } else {
      console.log('✅ Joelle has correct hr-admin role')
    }

    // Test 3: Check if active
    if (!joelleRecord.active) {
      console.error('❌ Joelle is not active')
      
      // Activate the account
      console.log('🔧 Activating Joelle\'s account...')
      const { error: activateError } = await supabaseAdmin
        .from('appy_managers')
        .update({ active: true })
        .eq('email', '<EMAIL>')

      if (activateError) {
        console.error('❌ Failed to activate:', activateError.message)
      } else {
        console.log('✅ Account activated')
      }
    } else {
      console.log('✅ Joelle is active')
    }

    // Test 4: Check departments exist (needed for add employee)
    console.log('\n📋 Test 4: Checking departments...')
    const { data: departments, error: deptError } = await supabaseAdmin
      .from('appy_departments')
      .select('id, name')
      .limit(5)

    if (deptError) {
      console.error('❌ Error fetching departments:', deptError.message)
    } else {
      console.log('✅ Departments available:', departments?.length || 0)
      if (departments && departments.length > 0) {
        console.log('Sample departments:', departments.map(d => d.name))
      }
    }

    // Test 5: Check managers exist (needed for add employee)
    console.log('\n📋 Test 5: Checking managers...')
    const { data: managers, error: mgrsError } = await supabaseAdmin
      .from('appy_managers')
      .select('user_id, full_name, role')
      .eq('active', true)
      .limit(5)

    if (mgrsError) {
      console.error('❌ Error fetching managers:', mgrsError.message)
    } else {
      console.log('✅ Managers available:', managers?.length || 0)
      if (managers && managers.length > 0) {
        console.log('Sample managers:', managers.map(m => `${m.full_name} (${m.role})`))
      }
    }

    console.log('\n🎉 HR Admin setup test completed!')
    console.log('\n📝 Next steps:')
    console.log('1. Have Joelle log into the system')
    console.log('2. Visit /api/test-joelle-auth to check authentication')
    console.log('3. Visit /dashboard/add-employee to test the feature')

  } catch (error) {
    console.error('🚨 Test failed:', error)
  }
}

// Run the test
testHRAdminSetup()
