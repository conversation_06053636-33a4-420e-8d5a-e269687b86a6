"use client"

import { Badge } from "@/components/ui/badge"
import { Crown, User, Building } from "lucide-react"
import { cn } from "@/lib/utils"

interface ManagerDepartmentBadgeProps {
  managerName: string
  departmentName?: string | null
  isPrimary?: boolean
  showDepartment?: boolean
  variant?: "default" | "outline" | "secondary"
  size?: "sm" | "md" | "lg"
  className?: string
}

export function ManagerDepartmentBadge({
  managerName,
  departmentName,
  isPrimary = false,
  showDepartment = true,
  variant = "outline",
  size = "md",
  className
}: ManagerDepartmentBadgeProps) {
  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    md: "text-sm px-3 py-1",
    lg: "text-base px-4 py-2"
  }

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4", 
    lg: "h-5 w-5"
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Badge 
        variant={variant} 
        className={cn(
          sizeClasses[size],
          "flex items-center gap-1.5",
          isPrimary && "border-yellow-500 bg-yellow-50 text-yellow-700 dark:bg-yellow-950 dark:text-yellow-300"
        )}
      >
        {isPrimary ? (
          <Crown className={cn(iconSizes[size], "text-yellow-600 dark:text-yellow-400")} />
        ) : (
          <User className={cn(iconSizes[size], "text-muted-foreground")} />
        )}
        <span className="font-medium">{managerName}</span>
        {isPrimary && size !== "sm" && (
          <span className="text-xs opacity-75">Primary</span>
        )}
      </Badge>
      
      {showDepartment && departmentName && (
        <Badge 
          variant="secondary" 
          className={cn(
            sizeClasses[size],
            "flex items-center gap-1 bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800"
          )}
        >
          <Building className={cn(iconSizes[size])} />
          <span>{departmentName}</span>
        </Badge>
      )}
    </div>
  )
}

interface ManagerListDisplayProps {
  managers: Array<{
    managerName?: string
    departmentName?: string | null
    isPrimary?: boolean
  }>
  showDepartments?: boolean
  variant?: "default" | "outline" | "secondary"
  size?: "sm" | "md" | "lg"
  className?: string
  maxDisplay?: number
}

export function ManagerListDisplay({
  managers,
  showDepartments = true,
  variant = "outline",
  size = "md",
  className,
  maxDisplay = 3
}: ManagerListDisplayProps) {
  if (!managers || managers.length === 0) {
    return (
      <Badge variant="secondary" className={cn("text-muted-foreground", className)}>
        No managers assigned
      </Badge>
    )
  }

  const displayManagers = managers.slice(0, maxDisplay)
  const remainingCount = managers.length - maxDisplay

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {displayManagers.map((manager, index) => (
        <ManagerDepartmentBadge
          key={`${manager.managerName}-${index}`}
          managerName={manager.managerName || 'Unknown Manager'}
          departmentName={manager.departmentName}
          isPrimary={manager.isPrimary}
          showDepartment={showDepartments}
          variant={variant}
          size={size}
        />
      ))}
      
      {remainingCount > 0 && (
        <Badge variant="secondary" className={cn("text-muted-foreground", className)}>
          +{remainingCount} more
        </Badge>
      )}
    </div>
  )
}

interface CompactManagerDisplayProps {
  managers: Array<{
    managerName?: string
    departmentName?: string | null
    isPrimary?: boolean
  }>
  className?: string
}

export function CompactManagerDisplay({
  managers,
  className
}: CompactManagerDisplayProps) {
  if (!managers || managers.length === 0) {
    return <span className={cn("text-muted-foreground text-sm", className)}>No managers</span>
  }

  const primaryManager = managers.find(m => m.isPrimary)
  const otherManagers = managers.filter(m => !m.isPrimary)

  if (managers.length === 1) {
    const manager = managers[0]
    return (
      <span className={cn("text-sm", className)}>
        {manager.managerName}
        {manager.departmentName && (
          <span className="text-muted-foreground"> ({manager.departmentName})</span>
        )}
      </span>
    )
  }

  return (
    <span className={cn("text-sm", className)}>
      {primaryManager ? (
        <>
          <span className="font-medium">{primaryManager.managerName}</span>
          {primaryManager.departmentName && (
            <span className="text-muted-foreground"> ({primaryManager.departmentName})</span>
          )}
          {otherManagers.length > 0 && (
            <span className="text-muted-foreground"> +{otherManagers.length} more</span>
          )}
        </>
      ) : (
        <>
          <span className="font-medium">{managers[0].managerName}</span>
          {managers[0].departmentName && (
            <span className="text-muted-foreground"> ({managers[0].departmentName})</span>
          )}
          {managers.length > 1 && (
            <span className="text-muted-foreground"> +{managers.length - 1} more</span>
          )}
        </>
      )}
    </span>
  )
}
