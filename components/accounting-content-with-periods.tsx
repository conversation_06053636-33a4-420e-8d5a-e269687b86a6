"use client"

import { useState } from "react"
import { Calculator, DollarSign, FileText } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AccountingDashboardTable } from "@/components/accounting-dashboard-table"
import { AccountingStatsCards, AccountingSummary } from "@/components/accounting-stats-cards"
import type { AppraisalPeriod, AccountingStats, UserRole } from "@/lib/types"

interface AccountingContentWithPeriodsProps {
  initialData: any[]
  initialStats: AccountingStats
  periods: AppraisalPeriod[]
  currentUserRole?: UserRole
}

export function AccountingContentWithPeriods({ 
  initialData, 
  initialStats, 
  periods,
  currentUserRole
}: AccountingContentWithPeriodsProps) {
  const [accountingData, setAccountingData] = useState(initialData)
  const [accountingStats, setAccountingStats] = useState(initialStats)
  const [selectedPeriodId, setSelectedPeriodId] = useState(() => {
    // Default to current active period
    const currentPeriod = periods.find(p => !p.closed)
    return currentPeriod?.id || ''
  })
  const [loading, setLoading] = useState(false)

  // Handle period change
  const handlePeriodChange = async (periodId: string) => {
    try {
      setLoading(true)
      setSelectedPeriodId(periodId)
      
      // TODO: Implement API calls to fetch data for specific period
      // For now, we'll just use the initial data
      console.log('[ACCOUNTING] Period changed to:', periodId)
      
      // In a real implementation, you would:
      // const response = await fetch(`/api/accounting?periodId=${periodId}`)
      // const { data, stats } = await response.json()
      // setAccountingData(data)
      // setAccountingStats(stats)
      
    } catch (err) {
      console.error('[ACCOUNTING] Error loading data for period:', err)
    } finally {
      setLoading(false)
    }
  }

  // Get current period info for display
  const currentPeriod = periods.find(p => p.id === selectedPeriodId)
  const periodDisplayName = currentPeriod
    ? `${new Date(currentPeriod.periodStart).toLocaleDateString()} - ${new Date(currentPeriod.periodEnd).toLocaleDateString()}`
    : 'No period selected'

  return (
    <div className="space-y-6">
      {/* Summary Overview */}
      <AccountingSummary stats={accountingStats} />
      
      {/* Stats Cards */}
      <AccountingStatsCards stats={accountingStats} />
      
      {/* Main Data Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                Employee Payment Data
              </CardTitle>
              <CardDescription>
                {currentPeriod ? `${periodDisplayName} - ` : ''}
                Manage employee payments and export payroll data
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {/* Period Filter */}
              {periods.length > 0 && (
                <Select
                  value={selectedPeriodId}
                  onValueChange={handlePeriodChange}
                  disabled={loading}
                >
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select Period" />
                  </SelectTrigger>
                  <SelectContent>
                    {periods
                      .sort((a, b) => new Date(b.periodStart).getTime() - new Date(a.periodStart).getTime())
                      .map((period) => (
                        <SelectItem key={period.id} value={period.id}>
                          {new Date(period.periodStart).toLocaleDateString()} - {new Date(period.periodEnd).toLocaleDateString()}
                          {!period.closed && " (Active)"}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              )}
              <Badge variant="outline" className="bg-green-100 text-green-800">
                <Calculator className="mr-1 h-3 w-3" />
                {accountingData.length} Employee{accountingData.length !== 1 ? 's' : ''}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <AccountingDashboardTable data={accountingData} currentUserRole={currentUserRole} />
        </CardContent>
      </Card>
      
      {/* Help Information */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800 flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Export Information
          </CardTitle>
        </CardHeader>
        <CardContent className="text-blue-700">
          <div className="space-y-2 text-sm">
            <p>• <strong>Ready to Pay:</strong> Employees approved for payment processing</p>
            <p>• <strong>CSV Export:</strong> Only includes employees ready for payment or with submitted appraisals</p>
            <p>• <strong>Hours:</strong> Automatically calculated for hourly employees (160 hours standard)</p>
            <p>• <strong>Payment Status:</strong> Based on appraisal submission and approval status</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

console.log('💰 [ACCOUNTING] Accounting content with periods component loaded')
