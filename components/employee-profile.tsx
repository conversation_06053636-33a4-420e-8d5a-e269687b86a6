"use client"

import { Em<PERSON>loyee, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/types"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Users,
  Building,
  UserCheck,
  Shield
} from "lucide-react"
import { ProfileHeader } from "@/components/profile-header"
import { KPIDashboard } from "@/components/kpi-dashboard"

interface EmployeeProfileProps {
  employee: Employee & {
    kpis?: EmployeeKPI[]
  }
  canEdit?: boolean
  currentUserRole?: string
}

export function EmployeeProfile({ 
  employee, 
  canEdit = false,
  currentUserRole
}: EmployeeProfileProps) {

  // Check if current user can manage KPIs
  const canManageKPIs = 
    currentUserRole === 'hr-admin' || 
    currentUserRole === 'super-admin' ||
    currentUserRole === 'manager'

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      {/* Enhanced Header */}
      <ProfileHeader 
        employee={employee}
        canEdit={canEdit}
        currentUserRole={currentUserRole}
      />

      {/* Main Content Grid */}
      <div className="grid gap-8 lg:grid-cols-3">
        {/* Left Column - Bio & About */}
        <div className="lg:col-span-2 space-y-8">
          {/* Bio Section */}
          {employee.bio && (
            <Card className="transition-all duration-300 hover:shadow-lg">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900">
                    <UserCheck className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <CardTitle className="text-xl">About</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-base text-muted-foreground whitespace-pre-wrap leading-relaxed">
                  {employee.bio}
                </p>
              </CardContent>
            </Card>
          )}

          {/* KPI Dashboard */}
          <KPIDashboard 
            kpis={employee.kpis || []}
            canManageKPIs={canManageKPIs}
          />
        </div>

        {/* Right Column - Details & Compensation */}
        <div className="space-y-8">
          {/* Employment Details */}
          <Card className="transition-all duration-300 hover:shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900">
                  <Building className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <CardTitle className="text-lg">Employment Details</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Current employment status
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg transition-all duration-200 hover:bg-muted/70">
                  <span className="text-sm font-medium text-muted-foreground">Status</span>
                  <Badge variant={employee.active ? "default" : "secondary"}>
                    {employee.active ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg transition-all duration-200 hover:bg-muted/70">
                  <span className="text-sm font-medium text-muted-foreground">Type</span>
                  <span className="font-semibold capitalize">{employee.rate}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Reporting Structure */}
          {employee.managers && employee.managers.length > 0 && (
            <Card className="transition-all duration-300 hover:shadow-lg">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900">
                    <Users className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">Reports To</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Management hierarchy
                    </p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {employee.managers.map((manager) => (
                    <div key={manager.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg transition-all duration-200 hover:bg-muted/70 hover:shadow-sm">
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{manager.managerName}</span>
                          {manager.isPrimary && (
                            <Badge variant="outline" className="text-xs">Primary</Badge>
                          )}
                        </div>
                        {manager.departmentName && (
                          <span className="text-xs text-muted-foreground">
                            {manager.departmentName} Department
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

        </div>
      </div>
    </div>
  )
}