"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ApprovalsDashboardTable } from "@/components/approvals-dashboard-table"
import { toast } from "sonner"
import type { PendingApproval } from "@/lib/types"

interface ApprovalsDashboardWrapperProps {
  pendingApprovals: PendingApproval[]
  completedApprovals: PendingApproval[]
}

export function ApprovalsDashboardWrapper({ 
  pendingApprovals: initialPendingApprovals, 
  completedApprovals: initialCompletedApprovals 
}: ApprovalsDashboardWrapperProps) {
  const router = useRouter()
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>(initialPendingApprovals)
  const [completedApprovals, setCompletedApprovals] = useState<PendingApproval[]>(initialCompletedApprovals)
  const [loading, setLoading] = useState(false)

  const handleRefresh = () => {
    router.refresh()
  }

  const handleBulkAction = async (action: string, selectedIds: string[]) => {
    setLoading(true)
    try {
      const promises = selectedIds.map(stepId => {
        const approval = pendingApprovals.find(a => a.stepId === stepId)
        if (!approval) return Promise.resolve()
        
        return processApproval(approval, action === 'approve' ? 'approved' : 'rejected')
      })

      await Promise.all(promises)
      
      toast.success(
        `Successfully ${action === 'approve' ? 'approved' : 'rejected'} ${selectedIds.length} appraisal${selectedIds.length === 1 ? '' : 's'}`
      )
      
      // Refresh the page to get updated data
      router.refresh()
    } catch (error) {
      console.error('Bulk action error:', error)
      toast.error('Failed to process bulk action')
    } finally {
      setLoading(false)
    }
  }

  const handleApprovalAction = async (approval: PendingApproval, action: 'approve' | 'reject') => {
    setLoading(true)
    try {
      const status = action === 'approve' ? 'approved' : 'rejected'
      await processApproval(approval, status)
      
      toast.success(
        `Appraisal ${action === 'approve' ? 'approved' : 'rejected'} successfully!`
      )
      
      // Refresh the page to get updated data
      router.refresh()
    } catch (error) {
      console.error('Approval action error:', error)
      toast.error(`Failed to ${action} appraisal`)
    } finally {
      setLoading(false)
    }
  }

  const processApproval = async (approval: PendingApproval, status: 'approved' | 'rejected') => {
    const response = await fetch('/api/approvals/process', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        stepId: approval.stepId,
        action: status,
        comments: undefined, // Could be enhanced to collect comments
        rejectionReason: status === 'rejected' ? 'Bulk action rejection' : undefined
      })
    })

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.error || 'Failed to process approval')
    }

    return result
  }

  const handleViewAppraisal = (employeeId: string) => {
    router.push(`/dashboard/appraisal/${employeeId}`)
  }

  return (
    <ApprovalsDashboardTable
      pendingApprovals={pendingApprovals}
      completedApprovals={completedApprovals}
      onRefresh={handleRefresh}
      onBulkAction={handleBulkAction}
      onApprovalAction={handleApprovalAction}
      onViewAppraisal={handleViewAppraisal}
      loading={loading}
    />
  )
}