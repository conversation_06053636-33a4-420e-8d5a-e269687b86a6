"use client"

import * as React from "react"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type RowSelectionState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, Filter, Users, Clock, AlertTriangle, Calendar, Building2, CheckCircle, XCircle, Eye, MoreHorizontal, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { useIsMobile } from "@/components/ui/use-mobile"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu"
import type { PendingApproval } from "@/lib/types"
import { toast } from "sonner"

interface ApprovalsDashboardTableProps {
  pendingApprovals: PendingApproval[]
  completedApprovals: PendingApproval[]
  onRefresh?: () => void
  onBulkAction?: (action: string, selectedIds: string[]) => void
  onApprovalAction?: (approval: PendingApproval, action: 'approve' | 'reject') => void
  onViewAppraisal?: (employeeId: string) => void
  loading?: boolean
}

const priorityColors = {
  1: 'bg-red-100 text-red-800',
  2: 'bg-orange-100 text-orange-800',
  3: 'bg-blue-100 text-blue-800'
}

// Helper function to safely format dates
function formatDateSafe(dateString: string | undefined): string {
  if (!dateString) return '-'

  try {
    const date = new Date(dateString)
    return date.toLocaleDateString()
  } catch (error) {
    console.error('Date formatting error:', error)
    return '-'
  }
}

export function ApprovalsDashboardTable({
  pendingApprovals,
  completedApprovals,
  onRefresh,
  onBulkAction,
  onApprovalAction,
  onViewAppraisal,
  loading = false
}: ApprovalsDashboardTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  const [activeTab, setActiveTab] = React.useState<'pending' | 'completed'>('pending')
  const isMobile = useIsMobile()

  // Combine data based on active tab
  const data = activeTab === 'pending' ? pendingApprovals : completedApprovals

  // Get unique values for filters
  const departments = React.useMemo(() => {
    const depts = Array.from(new Set(data.map(item => item.departmentName).filter(Boolean)))
    return depts.sort()
  }, [data])

  const statuses = React.useMemo(() => {
    const statusList = Array.from(new Set(data.map(item => item.status).filter(Boolean))) as string[]
    return statusList.sort()
  }, [data])

  const levels = React.useMemo(() => {
    const levelList = Array.from(new Set(data.map(item => item.stepLevel)))
    return levelList.sort()
  }, [data])

  // Mobile card component for responsive design
  const MobileApprovalCard = ({ approval }: { approval: PendingApproval }) => {
    const isUrgent = approval.daysPending > 7
    const isOverdue = approval.daysPending > 14

    return (
      <Card key={approval.stepId} className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1 flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={rowSelection[approval.stepId] || false}
                  onCheckedChange={(checked) => {
                    setRowSelection(prev => ({
                      ...prev,
                      [approval.stepId]: !!checked
                    }))
                  }}
                  aria-label={`Select ${approval.employeeName}`}
                  className="touch-target-sm"
                />
                <h3 className="mobile-heading-2 leading-none truncate">{approval.employeeName}</h3>
              </div>
              <p className="mobile-caption text-muted-foreground truncate">{approval.departmentName}</p>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="touch-target flex-shrink-0"
                  aria-label={`Actions for ${approval.employeeName}`}
                >
                  <span className="sr-only">Open menu for {approval.employeeName}</span>
                  <MoreHorizontal className="h-5 w-5" aria-hidden="true" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Actions for {approval.employeeName}</DropdownMenuLabel>
                <DropdownMenuItem 
                  onClick={() => onViewAppraisal?.(approval.employeeId)}
                  className="flex items-center touch-target"
                >
                  <Eye className="mr-2 h-4 w-4" />
                  View Appraisal
                </DropdownMenuItem>
                {activeTab === 'pending' && (
                  <>
                    <DropdownMenuItem
                      onClick={() => onApprovalAction?.(approval, 'approve')}
                      className="flex items-center touch-target text-green-600"
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Approve
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => onApprovalAction?.(approval, 'reject')}
                      className="flex items-center touch-target text-red-600"
                    >
                      <XCircle className="mr-2 h-4 w-4" />
                      Reject
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="mobile-caption font-medium text-muted-foreground">Level:</span>
              <Badge 
                variant="outline" 
                className={priorityColors[approval.stepLevel as keyof typeof priorityColors] || 'bg-gray-100 text-gray-800'}
              >
                Level {approval.stepLevel}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="mobile-caption font-medium text-muted-foreground">Submitted:</span>
              <span className="mobile-body text-muted-foreground">
                {formatDateSafe(approval.submissionDate)}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="mobile-caption font-medium text-muted-foreground">Days Pending:</span>
              <div className="flex items-center gap-2">
                <span className={isOverdue ? 'text-red-600 font-medium' : isUrgent ? 'text-orange-600' : ''}>
                  {approval.daysPending} days
                </span>
                {isOverdue && <AlertTriangle className="h-4 w-4 text-red-600" />}
              </div>
            </div>
            {approval.status && (
              <div className="flex items-center justify-between">
                <span className="mobile-caption font-medium text-muted-foreground">Status:</span>
                <Badge 
                  variant="outline" 
                  className={approval.status === 'approved' 
                    ? 'bg-green-100 text-green-800' 
                    : approval.status === 'rejected'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-yellow-100 text-yellow-800'
                  }
                >
                  {approval.status === 'approved' && <CheckCircle className="h-3 w-3 mr-1" />}
                  {approval.status === 'rejected' && <XCircle className="h-3 w-3 mr-1" />}
                  {approval.status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
                  {approval.status === 'approved' ? 'Approved' : 
                   approval.status === 'rejected' ? 'Rejected' : 'Pending'}
                </Badge>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  const columns: ColumnDef<PendingApproval>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "employeeName",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <Users className="mr-2 h-4 w-4" />
          Employee
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("employeeName")}</div>
      ),
    },
    {
      accessorKey: "departmentName",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <Building2 className="mr-2 h-4 w-4" />
          Department
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <span className="text-sm">{row.getValue("departmentName")}</span>
      ),
    },
    {
      accessorKey: "stepLevel",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          Level
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const level = row.getValue("stepLevel") as number
        return (
          <Badge 
            variant="outline" 
            className={priorityColors[level as keyof typeof priorityColors] || 'bg-gray-100 text-gray-800'}
          >
            Level {level}
          </Badge>
        )
      },
    },
    {
      accessorKey: "submissionDate",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Submitted
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const submissionDate = row.getValue("submissionDate") as string
        return (
          <span className="text-sm">
            {formatDateSafe(submissionDate)}
          </span>
        )
      },
    },
    {
      accessorKey: "daysPending",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <Clock className="mr-2 h-4 w-4" />
          Days Pending
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const daysPending = row.getValue("daysPending") as number
        const isUrgent = daysPending > 7
        const isOverdue = daysPending > 14

        return (
          <div className="flex items-center gap-2">
            <span className={isOverdue ? 'text-red-600 font-medium' : isUrgent ? 'text-orange-600' : ''}>
              {daysPending} days
            </span>
            {isOverdue && <AlertTriangle className="h-4 w-4 text-red-600" />}
          </div>
        )
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        if (!status) return null

        return (
          <Badge 
            variant="outline" 
            className={status === 'approved' 
              ? 'bg-green-100 text-green-800' 
              : status === 'rejected'
              ? 'bg-red-100 text-red-800'
              : 'bg-yellow-100 text-yellow-800'
            }
          >
            {status === 'approved' && <CheckCircle className="h-3 w-3 mr-1" />}
            {status === 'rejected' && <XCircle className="h-3 w-3 mr-1" />}
            {status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
            {status === 'approved' ? 'Approved' : 
             status === 'rejected' ? 'Rejected' : 'Pending'}
          </Badge>
        )
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onViewAppraisal?.(row.original.employeeId)}
          >
            <Eye className="h-4 w-4 mr-1" />
            View
          </Button>
          {activeTab === 'pending' && (
            <>
              <Button
                size="sm"
                variant="outline"
                className="text-green-600 hover:text-green-700"
                onClick={() => onApprovalAction?.(row.original, 'approve')}
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                Approve
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="text-red-600 hover:text-red-700"
                onClick={() => onApprovalAction?.(row.original, 'reject')}
              >
                <XCircle className="h-4 w-4 mr-1" />
                Reject
              </Button>
            </>
          )}
        </div>
      ),
    },
  ]

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: { sorting, columnFilters, rowSelection },
    enableRowSelection: true,
    getRowId: (row) => row.stepId,
  })

  // Get selected rows
  const selectedRows = table.getFilteredSelectedRowModel().rows
  const selectedIds = selectedRows.map(row => row.original.stepId)

  const handleBulkAction = (action: string) => {
    if (onBulkAction && selectedIds.length > 0) {
      onBulkAction(action, selectedIds)
      setRowSelection({})
    }
  }

  return (
    <div className="space-y-4">
      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('pending')}
          className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            activeTab === 'pending'
              ? 'bg-background text-foreground shadow-sm'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          Pending ({pendingApprovals.length})
        </button>
        <button
          onClick={() => setActiveTab('completed')}
          className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            activeTab === 'completed'
              ? 'bg-background text-foreground shadow-sm'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          Completed ({completedApprovals.length})
        </button>
      </div>

      {/* Bulk Actions */}
      {selectedIds.length > 0 && activeTab === 'pending' && (
        <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">
              {selectedIds.length} item{selectedIds.length === 1 ? '' : 's'} selected
            </span>
          </div>
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="h-4 w-4 mr-2" />
                  Bulk Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Actions for {selectedIds.length} items</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleBulkAction('approve')}>
                  Approve Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('reject')}>
                  Reject Selected
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setRowSelection({})}
            >
              Clear Selection
            </Button>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-wrap gap-4 items-center">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Filters:</span>
        </div>
        
        <Input
          placeholder="Search employees..."
          value={(table.getColumn("employeeName")?.getFilterValue() as string) ?? ""}
          onChange={(event) => {
            table.getColumn("employeeName")?.setFilterValue(event.target.value)
          }}
          className="max-w-sm"
          disabled={loading}
        />

        <Select
          value={(table.getColumn("departmentName")?.getFilterValue() as string) ?? ""}
          onValueChange={(value) => {
            table.getColumn("departmentName")?.setFilterValue(value === "all" ? "" : value)
          }}
          disabled={loading}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="All Departments" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            {departments.map((dept) => (
              <SelectItem key={dept} value={dept}>
                {dept}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={(table.getColumn("stepLevel")?.getFilterValue() as string) ?? ""}
          onValueChange={(value) => {
            table.getColumn("stepLevel")?.setFilterValue(value === "all" ? "" : value)
          }}
          disabled={loading}
        >
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="All Levels" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Levels</SelectItem>
            {levels.map((level) => (
              <SelectItem key={level} value={level.toString()}>
                Level {level}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {activeTab === 'completed' && (
          <Select
            value={(table.getColumn("status")?.getFilterValue() as string) ?? ""}
            onValueChange={(value) => {
              table.getColumn("status")?.setFilterValue(value === "all" ? "" : value)
            }}
            disabled={loading}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="All Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              {statuses.map((status) => (
                <SelectItem key={status} value={status}>
                  {status === 'approved' ? 'Approved' : 
                   status === 'rejected' ? 'Rejected' : 'Pending'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {/* Loading overlay */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-pulse text-muted-foreground">Loading approvals...</div>
        </div>
      )}

      {/* Mobile card view */}
      {!loading && isMobile ? (
        <div className="space-y-4">
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <MobileApprovalCard key={row.id} approval={row.original} />
            ))
          ) : (
            <Card>
              <CardContent className="py-8">
                <p className="text-center text-muted-foreground">
                  No {activeTab} approvals found.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      ) : !loading ? (
        /* Desktop table view */
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No {activeTab} approvals found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      ) : null}

      {/* Pagination */}
      {!loading && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="text-sm text-muted-foreground">
            Showing {table.getFilteredRowModel().rows.length} of {data.length} {activeTab} approvals
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}