"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { StatusBadge } from "@/components/status-badge"
import { toast } from "sonner"
import type { AccountingViewData } from "@/lib/types"
import { Download } from "lucide-react"
import { announceToScreenReader, getTableAnnouncement } from "@/lib/accessibility"
import { useIsMobile } from "@/components/ui/use-mobile"

export function AccountingTable({ data }: { data: AccountingViewData[] }) {
  const isMobile = useIsMobile()

  // Mobile card component for responsive design
  const MobileAccountingCard = ({ item }: { item: AccountingViewData }) => (
    <Card key={item.employeeId} className="mb-4">
      <CardHeader className="pb-3">
        <div className="space-y-1">
          <h3 className="mobile-heading-2 leading-none">{item.employeeName}</h3>
          <p className="mobile-caption text-muted-foreground">{item.departmentName}</p>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="mobile-caption font-medium text-muted-foreground">Manager:</span>
            <span className="mobile-body">{item.managerName}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="mobile-caption font-medium text-muted-foreground">Status:</span>
            <StatusBadge status={item.status} />
          </div>
          <div className="flex items-center justify-between">
            <span className="mobile-caption font-medium text-muted-foreground">Submitted:</span>
            <span className="mobile-body text-muted-foreground">
              {item.submittedAt ? new Date(item.submittedAt).toLocaleString() : "N/A"}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
  const handleExport = () => {
    console.log('[A11Y] Starting CSV export process')
    const submittedAppraisals = data.filter((item) => item.status === "submitted")
    if (submittedAppraisals.length === 0) {
      toast.error("No submitted appraisals to export.")
      announceToScreenReader("No submitted appraisals available for export", "assertive")
      return
    }

    // Sanitize data to prevent CSV injection attacks
    const sanitizeCell = (value: string | number | null | undefined): string => {
      if (value === null || value === undefined) return ''

      const stringValue = String(value)

      // Remove dangerous characters that could be interpreted as formulas
      // Excel/Sheets interpret =, +, -, @ at the start as formulas
      const sanitized = stringValue.replace(/^[=+\-@]/, "'$&")

      // Escape quotes and wrap in quotes if contains comma, newline, or quote
      if (sanitized.includes(',') || sanitized.includes('\n') || sanitized.includes('"')) {
        return `"${sanitized.replace(/"/g, '""')}"`
      }

      return sanitized
    }

    const headers = ["Employee", "Hours", "Compensation Type", "Manager", "Timestamp"]
    const csvRows = [
      headers.join(","),
      ...submittedAppraisals.map((row) =>
        [
          sanitizeCell(row.employeeName),
          sanitizeCell(row.hours),
          sanitizeCell(row.compensation),
          sanitizeCell(row.managerName),
          sanitizeCell(row.submittedAt ? new Date(row.submittedAt).toISOString() : ""),
        ].join(","),
      ),
    ]

    const csvString = csvRows.join("\n")
    const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob)
      link.setAttribute("href", url)
      link.setAttribute("download", `payroll-approvals-${new Date().toISOString().split("T")[0]}.csv`)
      link.style.visibility = "hidden"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url) // Clean up the URL object
      toast.success(`Exported ${submittedAppraisals.length} submitted appraisals to CSV`)
      announceToScreenReader(`Successfully exported ${submittedAppraisals.length} submitted appraisals to CSV file`)
    } else {
      // Fallback for browsers that don't support download attribute
      toast.error("CSV export is not supported in this browser.")
      announceToScreenReader("CSV export is not supported in this browser", "assertive")
    }
  }

  return (
    <>
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-4">
        <div
          className="flex items-center gap-2 sm:gap-4 mobile-caption text-muted-foreground flex-wrap"
          role="group"
          aria-label="Status legend"
        >
          <span className="flex items-center gap-2" role="img" aria-label="Submitted status">
            <span className="h-2 w-2 rounded-full bg-green-500" aria-hidden="true" /> Submitted
          </span>
          <span className="flex items-center gap-2" role="img" aria-label="Draft status">
            <span className="h-2 w-2 rounded-full bg-yellow-500" aria-hidden="true" /> Draft
          </span>
          <span className="flex items-center gap-2" role="img" aria-label="Missing status">
            <span className="h-2 w-2 rounded-full bg-gray-400" aria-hidden="true" /> Missing
          </span>
        </div>
        <Button
          size="sm"
          variant="outline"
          onClick={handleExport}
          aria-label="Export submitted appraisals to CSV file"
          className="touch-target w-full sm:w-auto"
        >
          <Download className="mr-2 h-4 w-4" aria-hidden="true" />
          Export CSV
        </Button>
      </div>
      {/* Mobile card view */}
      {isMobile ? (
        <div className="space-y-4">
          {data.length > 0 ? (
            data.map((item) => (
              <MobileAccountingCard key={item.employeeId} item={item} />
            ))
          ) : (
            <Card>
              <CardContent className="py-8">
                <p className="text-center text-muted-foreground">No accounting data found.</p>
              </CardContent>
            </Card>
          )}
        </div>
      ) : (
        /* Desktop table view */
        <div className="rounded-md border">
          <Table
            aria-label={getTableAnnouncement(data.length, 5)}
            role="table"
          >
            <TableHeader>
              <TableRow role="row">
                <TableHead role="columnheader">Employee</TableHead>
                <TableHead role="columnheader">Department</TableHead>
                <TableHead role="columnheader">Manager</TableHead>
                <TableHead role="columnheader">Status</TableHead>
                <TableHead className="text-right" role="columnheader">Submitted At</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((item, index) => (
                <TableRow
                  key={item.employeeId}
                  role="row"
                  aria-rowindex={index + 2}
                >
                  <TableCell className="font-medium" role="gridcell">{item.employeeName}</TableCell>
                  <TableCell role="gridcell">{item.departmentName}</TableCell>
                  <TableCell role="gridcell">{item.managerName}</TableCell>
                  <TableCell role="gridcell">
                    <StatusBadge status={item.status} />
                  </TableCell>
                  <TableCell
                    className="text-right text-muted-foreground"
                    role="gridcell"
                    aria-label={item.submittedAt ? `Submitted on ${new Date(item.submittedAt).toLocaleString()}` : "Not submitted"}
                  >
                    {item.submittedAt ? new Date(item.submittedAt).toLocaleString() : "N/A"}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </>
  )
}
