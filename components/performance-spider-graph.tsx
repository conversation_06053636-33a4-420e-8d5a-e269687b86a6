"use client"

import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer, Legend, Tooltip } from 'recharts'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, TrendingDown, Minus, AlertCircle } from "lucide-react"
import { useErrorBoundary } from "@/components/error-boundary"
import type { PerformanceStats } from "@/lib/types"
import { useState, useEffect } from 'react'

interface PerformanceSpiderGraphProps {
  stats: PerformanceStats
  title?: string
  description?: string
}

export function PerformanceSpiderGraph({ 
  stats, 
  title = "Performance Distribution",
  description = "Overview of team performance ratings"
}: PerformanceSpiderGraphProps) {
  const { captureError } = useErrorBoundary()
  const [chartError, setChartError] = useState<string | null>(null)

  // Validate stats prop
  useEffect(() => {
    if (!stats) {
      setChartError("No performance data provided")
      return
    }
    
    // Check for invalid data
    if (typeof stats.total !== 'number' || stats.total < 0) {
      setChartError("Invalid performance data: total must be a non-negative number")
      return
    }
    
    setChartError(null)
  }, [stats])

  // Handle chart rendering errors
  const handleChartError = (error: Error) => {
    console.error('Chart rendering error:', error)
    setChartError("Failed to render performance chart")
  }

  // If there's a chart error, show fallback
  if (chartError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            {title}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
              <p className="text-lg font-medium">Chart Error</p>
              <p className="text-sm">{chartError}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Prepare data for the radar chart with error handling
  let radarData
  try {
    radarData = [
    {
      category: 'Exceeds Expectations',
      count: stats.exceedsExpectations,
      percentage: stats.total > 0 ? Math.round((stats.exceedsExpectations / stats.total) * 100) : 0,
      color: '#22c55e'
    },
    {
      category: 'Meets Expectations',
      count: stats.meetsExpectations,
      percentage: stats.total > 0 ? Math.round((stats.meetsExpectations / stats.total) * 100) : 0,
      color: '#3b82f6'
    },
    {
      category: 'Below Expectations',
      count: stats.belowExpectations,
      percentage: stats.total > 0 ? Math.round((stats.belowExpectations / stats.total) * 100) : 0,
      color: '#ef4444'
    },
    {
      category: 'Not Started',
      count: stats.notStarted,
      percentage: stats.total > 0 ? Math.round((stats.notStarted / stats.total) * 100) : 0,
      color: '#6b7280'
    },
    {
      category: 'Draft',
      count: stats.draftCount,
      percentage: stats.total > 0 ? Math.round((stats.draftCount / stats.total) * 100) : 0,
      color: '#f59e0b'
    }
  ]
  } catch (error) {
    console.error('Error preparing radar data:', error)
    setChartError("Failed to prepare chart data")
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            {title}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
              <p className="text-lg font-medium">Data Error</p>
              <p className="text-sm">Failed to prepare chart data</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-2 border border-gray-200 rounded shadow-lg">
          <p className="font-medium">{label}</p>
          <p className="text-sm">Count: {data.count}</p>
          <p className="text-sm">Percentage: {data.percentage}%</p>
        </div>
      )
    }
    return null
  }

  const getPerformanceTrend = () => {
    if (stats.total === 0) return null
    
    const positivePercentage = Math.round((stats.exceedsExpectations / stats.total) * 100)
    const negativePercentage = Math.round((stats.belowExpectations / stats.total) * 100)
    
    if (positivePercentage > negativePercentage) {
      return { trend: 'up', color: 'text-green-600', icon: TrendingUp }
    } else if (negativePercentage > positivePercentage) {
      return { trend: 'down', color: 'text-red-600', icon: TrendingDown }
    } else {
      return { trend: 'stable', color: 'text-gray-600', icon: Minus }
    }
  }

  const trend = getPerformanceTrend()

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
          <div className="min-w-0 flex-1">
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              {title}
              {trend && <trend.icon className={`h-4 w-4 sm:h-5 sm:w-5 ${trend.color}`} />}
            </CardTitle>
            <CardDescription className="text-sm">{description}</CardDescription>
          </div>
          <Badge variant="outline" className="text-xs sm:text-sm flex-shrink-0">
            {stats.total} Total
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        {stats.total === 0 ? (
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-lg font-medium">No data available</p>
              <p className="text-sm">Performance data will appear here once appraisals are submitted</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4 sm:space-y-6">
            {/* Radar Chart */}
            <div className="h-48 sm:h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart data={radarData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="category" tick={{ fontSize: 10 }} className="text-xs sm:text-sm" />
                  <PolarRadiusAxis 
                    angle={90} 
                    domain={[0, 100]} 
                    tick={{ fontSize: 8 }}
                    tickFormatter={(value) => `${value}%`}
                    className="text-xs"
                  />
                  <Radar
                    name="Performance Distribution"
                    dataKey="percentage"
                    stroke="#3b82f6"
                    fill="#3b82f6"
                    fillOpacity={0.3}
                    strokeWidth={2}
                  />
                  <Tooltip content={<CustomTooltip />} />
                </RadarChart>
              </ResponsiveContainer>
            </div>

            {/* Summary Stats */}
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-4">
              <div className="text-center p-2 sm:p-3 bg-green-50 rounded-lg min-h-[60px] flex flex-col justify-center">
                <div className="text-lg sm:text-2xl font-bold text-green-600">
                  {stats.exceedsExpectations}
                </div>
                <div className="text-xs text-green-600 font-medium">Exceeds</div>
              </div>
              <div className="text-center p-2 sm:p-3 bg-blue-50 rounded-lg min-h-[60px] flex flex-col justify-center">
                <div className="text-lg sm:text-2xl font-bold text-blue-600">
                  {stats.meetsExpectations}
                </div>
                <div className="text-xs text-blue-600 font-medium">Meets</div>
              </div>
              <div className="text-center p-2 sm:p-3 bg-red-50 rounded-lg min-h-[60px] flex flex-col justify-center">
                <div className="text-lg sm:text-2xl font-bold text-red-600">
                  {stats.belowExpectations}
                </div>
                <div className="text-xs text-red-600 font-medium">Below</div>
              </div>
              <div className="text-center p-2 sm:p-3 bg-yellow-50 rounded-lg min-h-[60px] flex flex-col justify-center">
                <div className="text-lg sm:text-2xl font-bold text-yellow-600">
                  {stats.draftCount}
                </div>
                <div className="text-xs text-yellow-600 font-medium">Draft</div>
              </div>
              <div className="text-center p-2 sm:p-3 bg-gray-50 rounded-lg min-h-[60px] flex flex-col justify-center">
                <div className="text-lg sm:text-2xl font-bold text-gray-600">
                  {stats.notStarted}
                </div>
                <div className="text-xs text-gray-600 font-medium">Not Started</div>
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 pt-3 sm:pt-4 border-t">
              <div className="text-center p-3 bg-muted/30 rounded-lg">
                <div className="text-xs sm:text-sm text-muted-foreground">Completion Rate</div>
                <div className="text-base sm:text-lg font-semibold">
                  {stats.total > 0 ? Math.round((stats.submittedCount / stats.total) * 100) : 0}%
                </div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-xs sm:text-sm text-muted-foreground">High Performers</div>
                <div className="text-base sm:text-lg font-semibold text-green-600">
                  {stats.total > 0 ? Math.round((stats.exceedsExpectations / stats.total) * 100) : 0}%
                </div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-xs sm:text-sm text-muted-foreground">Need Support</div>
                <div className="text-base sm:text-lg font-semibold text-red-600">
                  {stats.total > 0 ? Math.round((stats.belowExpectations / stats.total) * 100) : 0}%
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}