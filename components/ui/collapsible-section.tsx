"use client"

import * as React from "react"
import { ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface CollapsibleSectionProps {
  title: string
  children: React.ReactNode
  defaultOpen?: boolean
  className?: string
  titleClassName?: string
  contentClassName?: string
}

export function CollapsibleSection({
  title,
  children,
  defaultOpen = false,
  className,
  titleClassName,
  contentClassName,
}: CollapsibleSectionProps) {
  const [isOpen, setIsOpen] = React.useState(defaultOpen)

  return (
    <div className={cn("border rounded-lg", className)}>
      <Button
        variant="ghost"
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "w-full justify-between p-4 h-auto touch-target",
          titleClassName
        )}
        aria-expanded={isOpen}
      >
        <span className="mobile-heading-2 text-left">{title}</span>
        <ChevronDown
          className={cn(
            "h-4 w-4 transition-transform duration-200",
            isOpen && "rotate-180"
          )}
        />
      </Button>
      
      {isOpen && (
        <div className={cn("p-4 pt-0 border-t", contentClassName)}>
          {children}
        </div>
      )}
    </div>
  )
}
