"use client"

import { useState, useEffect } from "react"
import { AppraisalDashboardTable } from "@/components/appraisal-dashboard-table"
import { bulkAppraisalAction, getManagerAppraisalsByPeriodAction } from "@/lib/actions"
import { toast } from "sonner"
import type { EmployeeAppraisal, Manager, AppraisalPeriod } from "@/lib/types"

interface AppraisalDashboardWrapperProps {
  data: EmployeeAppraisal[]
  managers: Manager[]
  periods: AppraisalPeriod[]
}

export function AppraisalDashboardWrapper({ data: initialData, managers, periods }: AppraisalDashboardWrapperProps) {
  const [data, setData] = useState<EmployeeAppraisal[]>(initialData)
  const [selectedPeriodId, setSelectedPeriodId] = useState<string>("")
  const [loading, setLoading] = useState(false)

  // Set default to current active period
  useEffect(() => {
    const activePeriod = periods.find(p => !p.closed)
    if (activePeriod && !selectedPeriodId) {
      setSelectedPeriodId(activePeriod.id)
    }
  }, [periods, selectedPeriodId])

  const handlePeriodChange = async (periodId: string) => {
    if (periodId === selectedPeriodId) return
    
    setLoading(true)
    setSelectedPeriodId(periodId)
    
    try {
      const result = await getManagerAppraisalsByPeriodAction(periodId || undefined)
      if (result.success && 'data' in result) {
        setData(result.data)
      } else {
        toast.error('error' in result ? result.error : 'Failed to load appraisals for selected period')
      }
    } catch (error) {
      console.error('Failed to load appraisals for period:', error)
      toast.error('Failed to load appraisals for selected period')
    } finally {
      setLoading(false)
    }
  }

  const handleBulkAction = async (action: string, selectedIds: string[]) => {
    try {
      const result = await bulkAppraisalAction(action, selectedIds)
      if (result.success) {
        toast.success(result.message || 'Action completed successfully')
        // Revalidate the page to show updated data
        window.location.reload()
      } else {
        toast.error(result.error || 'Action failed')
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Bulk action error:', error)
    }
  }

  return (
    <AppraisalDashboardTable 
      data={data} 
      managers={managers} 
      periods={periods}
      selectedPeriodId={selectedPeriodId}
      onPeriodChange={handlePeriodChange}
      onBulkAction={handleBulkAction}
      loading={loading}
    />
  )
}