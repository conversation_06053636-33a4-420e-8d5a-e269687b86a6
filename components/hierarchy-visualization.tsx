"use client"

import React, { useState, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Users, User, Crown, Shield, UserCheck, Search, Filter, ArrowUpDown, GitBranch } from "lucide-react"
import type { HierarchyNode, Department } from "@/lib/types"

interface HierarchyVisualizationProps {
  data: HierarchyNode[]
  departments: Department[]
}

type SortOption = 'name-asc' | 'name-desc' | 'role-hierarchy' | 'role-reverse' | 'department'
type ViewMode = 'company' | 'department'

function getRoleIcon(role: string) {
  switch (role) {
    case 'super-admin':
      return <Crown className="h-4 w-4" />
    case 'hr-admin':
      return <Shield className="h-4 w-4" />
    case 'senior-manager':
      return <UserCheck className="h-4 w-4" />
    case 'manager':
      return <Users className="h-4 w-4" />
    default:
      return <User className="h-4 w-4" />
  }
}

function getRoleColor(role: string) {
  switch (role) {
    case 'super-admin':
      return 'bg-purple-100 text-purple-800 border-purple-200'
    case 'hr-admin':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'senior-manager':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'manager':
      return 'bg-orange-100 text-orange-800 border-orange-200'
    case 'employee':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

function HierarchyNodeComponent({ node, level = 0 }: { node: HierarchyNode; level?: number }) {
  const router = useRouter()
  const hasChildren = node.children.length > 0
  const marginLeft = level * 24
  const maxRenderDepth = 15 // Prevent excessive nesting in UI

  // Prevent rendering beyond reasonable depth to avoid performance issues
  if (level > maxRenderDepth) {
    console.warn(`[PERF] Maximum render depth (${maxRenderDepth}) reached for node: ${node.id}`)
    return (
      <div className="text-xs text-muted-foreground p-2 border rounded">
        ... (max depth reached)
      </div>
    )
  }

  const handleNodeClick = () => {
    if (node.type === 'manager') {
      // Navigate to internal profile for managers
      router.push(`/dashboard/employees/${node.id}/profile`)
    } else {
      // Navigate to public profile for employees (or internal based on permissions)
      router.push(`/profile/${node.id}`)
    }
  }

  return (
    <div className="relative">
      {/* Connecting line from parent */}
      {level > 0 && (
        <div 
          className="absolute top-6 bg-gray-300"
          style={{
            left: `${marginLeft - 12}px`,
            width: '12px',
            height: '1px'
          }}
        />
      )}
      
      {/* Vertical line for children */}
      {hasChildren && (
        <div 
          className="absolute bg-gray-300"
          style={{
            left: `${marginLeft + 8}px`,
            top: '48px',
            width: '1px',
            height: `${node.children.length * 80}px`
          }}
        />
      )}

      <div className="flex items-start space-x-3 mb-4" style={{ marginLeft: `${marginLeft}px` }}>
        {/* Node indicator */}
        <div className="relative z-10 mt-4">
          <div className="w-4 h-4 bg-white border-2 border-gray-300 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-blue-500 rounded-full" />
          </div>
        </div>

        {/* Node content - Made clickable */}
        <Card 
          className="flex-1 max-w-sm cursor-pointer hover:shadow-md transition-shadow duration-200"
          onClick={handleNodeClick}
        >
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-sm hover:text-blue-600 transition-colors">
                {node.name}
              </h4>
              <div className="flex items-center space-x-1">
                {getRoleIcon(node.role)}
              </div>
            </div>
            
            <div className="space-y-2">
              <Badge 
                variant="outline" 
                className={`text-xs ${getRoleColor(node.role)}`}
              >
                {node.role.replace('-', ' ').toUpperCase()}
              </Badge>
              
              <p className="text-xs text-muted-foreground">
                {node.department}
              </p>
              
              {hasChildren && (
                <p className="text-xs text-muted-foreground">
                  {node.children.filter(c => c.type === 'manager').length} managers, {' '}
                  {node.children.filter(c => c.type === 'employee').length} employees
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Render children */}
      {hasChildren && (
        <div className="space-y-2">
          {node.children.map((child, index) => (
            <HierarchyNodeComponent
              key={child.id}
              node={child}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function HierarchyVisualization({ data, departments }: HierarchyVisualizationProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all')
  const [selectedRoles, setSelectedRoles] = useState<string[]>([])
  const [sortOption, setSortOption] = useState<SortOption>('name-asc')
  const [viewMode, setViewMode] = useState<ViewMode>('company')

  // Flatten hierarchy for filtering and searching with circular reference protection
  const flattenHierarchy = (nodes: HierarchyNode[]): HierarchyNode[] => {
    const flattened: HierarchyNode[] = []
    const visited = new Set<string>()
    const maxDepth = 20 // Prevent infinite recursion

    const traverse = (node: HierarchyNode, depth: number = 0) => {
      // Prevent infinite recursion with depth limit
      if (depth > maxDepth) {
        console.warn(`[PERF] Maximum hierarchy depth (${maxDepth}) reached for node: ${node.id}`)
        return
      }

      // Detect circular references
      if (visited.has(node.id)) {
        console.warn(`[PERF] Circular reference detected for node: ${node.id} at depth ${depth}`)
        return
      }

      visited.add(node.id)
      flattened.push(node)

      // Recursively traverse children
      node.children.forEach(child => traverse(child, depth + 1))

      // Remove from visited set to allow the same node in different branches
      visited.delete(node.id)
    }

    nodes.forEach(node => traverse(node))
    return flattened
  }

  // Get role hierarchy order for sorting
  const getRoleOrder = (role: string): number => {
    const order = {
      'super-admin': 1,
      'hr-admin': 2,
      'senior-manager': 3,
      'manager': 4,
      'employee': 5
    }
    return order[role as keyof typeof order] || 6
  }

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {  
    if (!data || data.length === 0) return []

    let filteredNodes = flattenHierarchy(data)

    // Apply search filter
    if (searchTerm) {
      filteredNodes = filteredNodes.filter(node =>
        node.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply department filter
    if (selectedDepartment !== 'all') {
      filteredNodes = filteredNodes.filter(node =>
        node.department === selectedDepartment
      )
    }

    // Apply role filter
    if (selectedRoles.length > 0) {
      filteredNodes = filteredNodes.filter(node =>
        selectedRoles.includes(node.role)
      )
    }

    // Apply sorting
    filteredNodes.sort((a, b) => {
      switch (sortOption) {
        case 'name-asc':
          return a.name.localeCompare(b.name)
        case 'name-desc':
          return b.name.localeCompare(a.name)
        case 'role-hierarchy':
          return getRoleOrder(a.role) - getRoleOrder(b.role)
        case 'role-reverse':
          return getRoleOrder(b.role) - getRoleOrder(a.role)
        case 'department':
          return a.department.localeCompare(b.department)
        default:
          return 0
      }
    })

    return filteredNodes
  }, [data, searchTerm, selectedDepartment, selectedRoles, sortOption])

  // Group filtered data by department for display
  const departmentGroups = useMemo(() => {
    if (viewMode === 'company') {
      return filteredAndSortedData.reduce((groups, node) => {
        const dept = node.department
        if (!groups[dept]) {
          groups[dept] = []
        }
        groups[dept].push(node)
        return groups
      }, {} as Record<string, HierarchyNode[]>)
    } else {
      // For department view, rebuild hierarchy structure
      return data.reduce((groups, node) => {
        const dept = node.department
        if (!groups[dept]) {
          groups[dept] = []
        }
        groups[dept].push(node)
        return groups
      }, {} as Record<string, HierarchyNode[]>)
    }
  }, [filteredAndSortedData, data, viewMode])

  const handleRoleToggle = (role: string) => {
    setSelectedRoles(prev => 
      prev.includes(role) 
        ? prev.filter(r => r !== role)
        : [...prev, role]
    )
  }

  const clearAllFilters = () => {
    setSearchTerm('')
    setSelectedDepartment('all')
    setSelectedRoles([])
  }

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-8">
        <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">No hierarchy data available</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Filter and Sort Controls */}
      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
        {/* Search and Department Filter Row */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="All Departments" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments.map((dept) => (
                <SelectItem key={dept.id} value={dept.name}>
                  {dept.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={sortOption} onValueChange={(value) => setSortOption(value as SortOption)}>
            <SelectTrigger className="w-full md:w-48">
              <ArrowUpDown className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name-asc">Name (A-Z)</SelectItem>
              <SelectItem value="name-desc">Name (Z-A)</SelectItem>
              <SelectItem value="role-hierarchy">Role (Senior First)</SelectItem>
              <SelectItem value="role-reverse">Role (Junior First)</SelectItem>
              <SelectItem value="department">Department</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Role Filter and View Mode Row */}
        <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium mr-2">Filter by Role:</span>
            {[
              { role: 'super-admin', label: 'Super Admin' },
              { role: 'hr-admin', label: 'HR Admin' },
              { role: 'senior-manager', label: 'Senior Manager' },
              { role: 'manager', label: 'Manager' },
              { role: 'employee', label: 'Employee' }
            ].map(({ role, label }) => (
              <Button
                key={role}
                variant={selectedRoles.includes(role) ? "default" : "outline"}
                size="sm"
                onClick={() => handleRoleToggle(role)}
                className="text-xs"
              >
                {getRoleIcon(role)}
                <span className="ml-1">{label}</span>
              </Button>
            ))}
          </div>

          <div className="flex gap-2 ml-auto">
            <Button
              variant={viewMode === 'company' ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode('company')}
            >
              <GitBranch className="h-4 w-4 mr-1" />
              Company View
            </Button>
            <Button
              variant={viewMode === 'department' ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode('department')}
            >
              <Users className="h-4 w-4 mr-1" />
              Department View
            </Button>
          </div>
        </div>

        {/* Active Filters and Clear Button */}
        {(searchTerm || selectedDepartment !== 'all' || selectedRoles.length > 0) && (
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex flex-wrap gap-2 items-center">
              <span className="text-sm text-muted-foreground">Active filters:</span>
              {searchTerm && (
                <Badge variant="secondary">Search: "{searchTerm}"</Badge>
              )}
              {selectedDepartment !== 'all' && (
                <Badge variant="secondary">Department: {selectedDepartment}</Badge>
              )}
              {selectedRoles.map(role => (
                <Badge key={role} variant="secondary">
                  Role: {role.replace('-', ' ')}
                </Badge>
              ))}
            </div>
            <Button variant="ghost" size="sm" onClick={clearAllFilters}>
              <Filter className="h-4 w-4 mr-1" />
              Clear All
            </Button>
          </div>
        )}
      </div>

      {/* Results Count */}
      <div className="text-sm text-muted-foreground">
        Showing {filteredAndSortedData.length} people
        {viewMode === 'department' && ' in hierarchical structure'}
      </div>

      {/* Legend */}
      <div className="flex flex-wrap gap-2 p-4 bg-gray-50 rounded-lg">
        <div className="text-sm font-medium mr-4">Roles:</div>
        {[
          { role: 'super-admin', label: 'Super Admin' },
          { role: 'hr-admin', label: 'HR Admin' },
          { role: 'senior-manager', label: 'Senior Manager' },
          { role: 'manager', label: 'Manager' },
          { role: 'employee', label: 'Employee' }
        ].map(({ role, label }) => (
          <div key={role} className="flex items-center space-x-1">
            {getRoleIcon(role)}
            <Badge variant="outline" className={`text-xs ${getRoleColor(role)}`}>
              {label}
            </Badge>
          </div>
        ))}
        <div className="ml-4 text-xs text-muted-foreground">
          Click on any person to view their profile
        </div>
      </div>

      {/* Hierarchy Display */}
      {filteredAndSortedData.length === 0 ? (
        <div className="text-center py-8">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No people match your current filters</p>
          <Button variant="ghost" onClick={clearAllFilters} className="mt-2">
            Clear filters to see all results
          </Button>
        </div>
      ) : (
        <div className="space-y-12">
          {viewMode === 'company' ? (
            // Flat list view grouped by department  
            Object.entries(departmentGroups).map(([department, nodes]) => (
              <div key={department} className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
                  {department} Department ({nodes.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {nodes.map(node => (
                    <HierarchyNodeComponent key={node.id} node={{...node, children: []}} level={0} />
                  ))}
                </div>
              </div>
            ))
          ) : (
            // Hierarchical tree view
            Object.entries(departmentGroups).map(([department, nodes]) => (
              <div key={department} className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
                  {department} Department
                </h3>
                <div className="space-y-4">
                  {nodes.map(node => (
                    <HierarchyNodeComponent key={node.id} node={node} />
                  ))}
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  )
}