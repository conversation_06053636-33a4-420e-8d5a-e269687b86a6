"use client"

import * as React from "react"
import { flexRender, getCoreRowModel, useReactTable, type ColumnDef } from "@tanstack/react-table"
import { MoreHorizontal, Edit, Calendar } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import type { AppraisalPeriod } from "@/lib/types"
import { PeriodFormDialog } from "./period-form-dialog"
import { useIsMobile } from "@/hooks/use-mobile"

export function PeriodsTable({ data }: { data: AppraisalPeriod[] }) {
  const [isFormOpen, setIsFormOpen] = React.useState(false)
  const [selectedPeriod, setSelectedPeriod] = React.useState<AppraisalPeriod | null>(null)
  const isMobile = useIsMobile()

  const handleEdit = (period: AppraisalPeriod) => {
    setSelectedPeriod(period)
    setIsFormOpen(true)
  }

  const handleAddNew = () => {
    setSelectedPeriod(null)
    setIsFormOpen(true)
  }

  const handleFormClose = () => {
    setIsFormOpen(false)
    setTimeout(() => setSelectedPeriod(null), 300)
  }

  const columns: ColumnDef<AppraisalPeriod>[] = [
    {
      accessorKey: "periodStart",
      header: "Start Date",
      cell: ({ row }) => new Date(row.getValue("periodStart")).toLocaleDateString(),
    },
    {
      accessorKey: "periodEnd",
      header: "End Date",
      cell: ({ row }) => new Date(row.getValue("periodEnd")).toLocaleDateString(),
    },
    {
      accessorKey: "closed",
      header: "Status",
      cell: ({ row }) => {
        const isClosed = row.getValue("closed")
        return (
          <Badge variant={!isClosed ? "default" : "outline"} className={!isClosed ? "bg-blue-600" : ""}>
            {!isClosed ? "Open" : "Closed"}
          </Badge>
        )
      },
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEdit(row.original)}>Edit Period</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  // Mobile card component for responsive design
  const MobilePeriodCard = ({ period }: { period: AppraisalPeriod }) => (
    <Card key={period.id} className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-2 flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <h3 className="mobile-heading-2 leading-none">Appraisal Period</h3>
            </div>
            <Badge
              variant={!period.closed ? "default" : "outline"}
              className={!period.closed ? "bg-blue-600" : ""}
            >
              {!period.closed ? "Open" : "Closed"}
            </Badge>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="touch-target flex-shrink-0"
                aria-label="Actions for appraisal period"
              >
                <span className="sr-only">Open menu for period</span>
                <MoreHorizontal className="h-5 w-5" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleEdit(period)} className="touch-target">
                <Edit className="mr-2 h-4 w-4" />
                Edit Period
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm font-medium text-muted-foreground">Start Date:</span>
              <p className="text-sm">{new Date(period.periodStart).toLocaleDateString()}</p>
            </div>
            <div>
              <span className="text-sm font-medium text-muted-foreground">End Date:</span>
              <p className="text-sm">{new Date(period.periodEnd).toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div>
      {/* Header with add button */}
      <div className="flex items-center py-4">
        <Button onClick={handleAddNew} className="w-full sm:w-auto sm:ml-auto min-h-[44px]">
          Open New Period
        </Button>
      </div>

      {/* Mobile card view */}
      {isMobile ? (
        <div className="space-y-4">
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <MobilePeriodCard key={row.id} period={row.original} />
            ))
          ) : (
            <Card>
              <CardContent className="py-8">
                <p className="text-center text-muted-foreground">No periods found.</p>
              </CardContent>
            </Card>
          )}
        </div>
      ) : (
        /* Desktop table view */
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      <PeriodFormDialog isOpen={isFormOpen} onClose={handleFormClose} period={selectedPeriod} />
    </div>
  )
}
