'use client'

import { useEffect, useState } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Shield, AlertTriangle, ArrowLeft } from 'lucide-react'
import type { UserRole } from '@/lib/schemas'

interface RoleGuardProps {
  children: React.ReactNode
  allowedRoles: UserRole[]
  userRole: UserRole
  fallback?: React.ReactNode
  redirectTo?: string
}

interface PermissionGuardProps {
  children: React.ReactNode
  permission: string
  userRole: UserRole
  fallback?: React.ReactNode
}

// Role hierarchy for access control
const roleHierarchy: Record<UserRole, number> = {
  'manager': 1,
  'accountant': 2,
  'admin': 3,
  'senior-manager': 4,
  'hr-admin': 5,
  'super-admin': 6,
}

// Permission mapping
const rolePermissions: Record<UserRole, string[]> = {
  'manager': [
    'appraisal:read',
    'appraisal:write',
    'employee:read',
  ],
  'accountant': [
    'appraisal:read',
    'approval:read',
    'approval:export',
    'employee:read',
  ],
  'hr-admin': [
    'employee:read',
    'employee:write',
    'employee:delete',
    'department:read',
    'department:write',
    'department:delete',
    'period:read',
    'period:write',
    'period:delete',
    'appraisal:read',
    'appraisal:write',
    'appraisal:approve',
    'approval:read',
    'approval:export',
  ],
  'admin': [
    'employee:read',
    'department:read',
    'period:read',
    'appraisal:read',
    'approval:read',
  ],
  'senior-manager': [
    'employee:read',
    'employee:write',
    'department:read',
    'period:read',
    'appraisal:read',
    'appraisal:write',
    'appraisal:approve',
    'approval:read',
  ],
  'super-admin': ['*'], // All permissions
}

function hasRole(userRole: UserRole, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(userRole)
}

function hasPermission(userRole: UserRole, permission: string): boolean {
  const permissions = rolePermissions[userRole] || []
  return permissions.includes('*') || permissions.includes(permission)
}

function AccessDeniedFallback({ 
  userRole, 
  requiredRoles, 
  onGoBack 
}: { 
  userRole: UserRole
  requiredRoles?: UserRole[]
  onGoBack?: () => void
}) {
  return (
    <div className="flex items-center justify-center min-h-[400px] p-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <Shield className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl">Access Denied</CardTitle>
          <CardDescription>
            You don't have permission to access this resource.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Your current role ({userRole.replace('-', ' ')}) doesn't have access to this page.
              {requiredRoles && (
                <>
                  <br />
                  Required roles: {requiredRoles.map(role => role.replace('-', ' ')).join(', ')}
                </>
              )}
            </AlertDescription>
          </Alert>
          
          <div className="flex flex-col gap-2">
            {onGoBack && (
              <Button onClick={onGoBack} variant="outline" className="w-full">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            )}
            
            <Button 
              onClick={() => window.location.href = '/dashboard'} 
              className="w-full"
            >
              Return to Dashboard
            </Button>
          </div>
          
          <p className="text-xs text-muted-foreground text-center">
            If you believe this is an error, please contact your system administrator.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

export function RoleGuard({ 
  children, 
  allowedRoles, 
  userRole, 
  fallback,
  redirectTo 
}: RoleGuardProps) {
  const router = useRouter()
  const [shouldRedirect, setShouldRedirect] = useState(false)

  useEffect(() => {
    if (redirectTo && !hasRole(userRole, allowedRoles)) {
      setShouldRedirect(true)
      const timer = setTimeout(() => {
        router.push(redirectTo)
      }, 3000) // Redirect after 3 seconds

      return () => clearTimeout(timer)
    }
  }, [userRole, allowedRoles, redirectTo, router])

  if (!hasRole(userRole, allowedRoles)) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <AccessDeniedFallback 
        userRole={userRole}
        requiredRoles={allowedRoles}
        onGoBack={() => window.history.back()}
      />
    )
  }

  return <>{children}</>
}

export function PermissionGuard({ 
  children, 
  permission, 
  userRole, 
  fallback 
}: PermissionGuardProps) {
  if (!hasPermission(userRole, permission)) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <AccessDeniedFallback 
        userRole={userRole}
        onGoBack={() => window.history.back()}
      />
    )
  }

  return <>{children}</>
}

// Hook for checking permissions in components
export function usePermissions(userRole: UserRole) {
  return {
    hasRole: (roles: UserRole[]) => hasRole(userRole, roles),
    hasPermission: (permission: string) => hasPermission(userRole, permission),
    canRead: (resource: string) => hasPermission(userRole, `${resource}:read`),
    canWrite: (resource: string) => hasPermission(userRole, `${resource}:write`),
    canDelete: (resource: string) => hasPermission(userRole, `${resource}:delete`),
    isManager: userRole === 'manager',
    isAccountant: userRole === 'accountant',
    isAdmin: userRole === 'admin',
    isSeniorManager: userRole === 'senior-manager',
    isHRAdmin: userRole === 'hr-admin',
    isSuperAdmin: userRole === 'super-admin',
    isAdminLevel: ['admin', 'senior-manager', 'hr-admin', 'super-admin'].includes(userRole),
  }
}

// Component for conditionally rendering based on permissions
export function ConditionalRender({
  condition,
  children,
  fallback = null,
}: {
  condition: boolean
  children: React.ReactNode
  fallback?: React.ReactNode
}) {
  return condition ? <>{children}</> : <>{fallback}</>
}

console.log('🛡️ Role-based access control components loaded')
