import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import type { AppraisalStatus } from "@/lib/types"
import { getStatusAnnouncement } from "@/lib/accessibility"

const statusMap: Record<AppraisalStatus, { label: string; className: string; description: string }> = {
  submitted: {
    label: "Submitted",
    description: "Appraisal has been submitted for review",
    className:
      "bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-400 border-blue-200 dark:border-blue-700",
  },
  approved: {
    label: "Approved",
    description: "Appraisal has been approved",
    className:
      "bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-400 border-green-200 dark:border-green-700",
  },
  rejected: {
    label: "Rejected",
    description: "Appraisal has been rejected and needs revision",
    className:
      "bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-400 border-red-200 dark:border-red-700",
  },
  draft: {
    label: "Draft",
    description: "Appraisal is saved as draft and not yet submitted",
    className:
      "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/40 dark:text-yellow-400 border-yellow-200 dark:border-yellow-700",
  },
  "not-started": {
    label: "Not Started",
    description: "Appraisal has not been started yet",
    className: "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-700",
  },
  "ready-to-pay": {
    label: "Ready to Pay",
    description: "All appraisals completed and employee is ready for payment",
    className: "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/40 dark:text-emerald-400 border-emerald-200 dark:border-emerald-700",
  },
  "senior-needed": {
    label: "Senior Approval Needed",
    description: "Waiting for senior manager approval before payment processing",
    className: "bg-purple-100 text-purple-800 dark:bg-purple-900/40 dark:text-purple-400 border-purple-200 dark:border-purple-700",
  },
  "contact-manager": {
    label: "Contact Manager",
    description: "Missing appraisals or issues requiring manager attention",
    className: "bg-orange-100 text-orange-800 dark:bg-orange-900/40 dark:text-orange-400 border-orange-200 dark:border-orange-700",
  },
}

export function StatusBadge({ status }: { status: AppraisalStatus }) {
  console.log('🏷️ [DEBUG] StatusBadge - Rendering status:', status)

  // Handle edge case where status might be undefined or invalid
  if (!status || !statusMap[status]) {
    console.warn('⚠️ [WARN] StatusBadge - Invalid status:', status)
    return (
      <Badge variant="outline" className="font-medium bg-gray-100 text-gray-800">
        Unknown
      </Badge>
    )
  }

  const { label, className, description } = statusMap[status]
  return (
    <Badge
      variant="outline"
      className={cn("font-medium", className)}
      aria-label={getStatusAnnouncement(status)}
      title={description}
    >
      {label}
    </Badge>
  )
}
