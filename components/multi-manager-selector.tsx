'use client'

import React from 'react'
import { Check, ChevronsUpDown, X, Crown, User } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import type { Manager } from '@/lib/types'

interface SelectedManager {
  id: string
  name: string
  isPrimary: boolean
}

interface MultiManagerSelectorProps {
  managers: Manager[]
  selectedManagers: SelectedManager[]
  onManagersChange: (managers: SelectedManager[]) => void
  disabled?: boolean
  placeholder?: string
  className?: string
}

export function MultiManagerSelector({
  managers,
  selectedManagers,
  onManagersChange,
  disabled = false,
  placeholder = "Select managers...",
  className
}: MultiManagerSelectorProps) {
  const [open, setOpen] = React.useState(false)

  const handleSelectManager = (managerId: string, managerName: string) => {
    const isAlreadySelected = selectedManagers.some(m => m.id === managerId)
    
    if (isAlreadySelected) {
      // Remove manager
      const updated = selectedManagers.filter(m => m.id !== managerId)
      onManagersChange(updated)
    } else {
      // Add manager (not primary by default)
      const updated = [...selectedManagers, { 
        id: managerId, 
        name: managerName, 
        isPrimary: selectedManagers.length === 0 // First manager becomes primary
      }]
      onManagersChange(updated)
    }
  }

  const handleSetPrimary = (managerId: string) => {
    const updated = selectedManagers.map(manager => ({
      ...manager,
      isPrimary: manager.id === managerId
    }))
    onManagersChange(updated)
  }

  const handleRemoveManager = (managerId: string) => {
    const managerToRemove = selectedManagers.find(m => m.id === managerId)
    const updated = selectedManagers.filter(m => m.id !== managerId)
    
    // If we removed the primary manager and there are others, make the first one primary
    if (managerToRemove?.isPrimary && updated.length > 0) {
      updated[0].isPrimary = true
    }
    
    onManagersChange(updated)
  }

  const primaryManager = selectedManagers.find(m => m.isPrimary)

  return (
    <div className={cn("space-y-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            disabled={disabled}
          >
            {selectedManagers.length === 0 ? (
              placeholder
            ) : (
              <span className="truncate">
                {selectedManagers.length === 1 
                  ? selectedManagers[0].name
                  : `${selectedManagers.length} managers selected`
                }
              </span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] min-w-[300px] p-0" align="start">
          <Command>
            <CommandInput placeholder="Search managers..." className="h-9" />
            <CommandEmpty>No managers found.</CommandEmpty>
            <CommandGroup>
              <ScrollArea className="h-48">
                {managers.map((manager) => {
                  const isSelected = selectedManagers.some(m => m.id === manager.id)
                  return (
                    <CommandItem
                      key={manager.id}
                      value={manager.fullName}
                      onSelect={() => handleSelectManager(manager.id, manager.fullName)}
                      className="cursor-pointer"
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4 shrink-0",
                          isSelected ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div className="flex flex-col gap-1 flex-1 min-w-0">
                        <span className="truncate">{manager.fullName}</span>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          {manager.email && (
                            <span className="truncate">{manager.email}</span>
                          )}
                          {manager.departmentName && (
                            <>
                              {manager.email && <span>•</span>}
                              <span className="truncate">{manager.departmentName}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </CommandItem>
                  )
                })}
              </ScrollArea>
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Selected Managers Display */}
      {selectedManagers.length > 0 && (
        <div className="space-y-2">
          <div className="text-sm font-medium text-muted-foreground">
            Selected Managers ({selectedManagers.length})
          </div>
          <div className="space-y-2">
            {selectedManagers.map((manager) => (
              <div
                key={manager.id}
                className="flex items-center justify-between p-3 bg-muted/50 rounded-md border"
              >
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  {manager.isPrimary ? (
                    <Crown className="h-4 w-4 text-yellow-600 shrink-0" />
                  ) : (
                    <User className="h-4 w-4 text-muted-foreground shrink-0" />
                  )}
                  <div className="flex flex-col gap-1 flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium truncate">{manager.name}</span>
                      {manager.isPrimary && (
                        <Badge variant="secondary" className="text-xs shrink-0">
                          Primary
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1 shrink-0">
                  {!manager.isPrimary && selectedManagers.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSetPrimary(manager.id)}
                      className="h-7 px-2 text-xs"
                      disabled={disabled}
                    >
                      Set Primary
                    </Button>
                  )}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveManager(manager.id)}
                    className="h-7 w-7 p-0 hover:bg-destructive/10 hover:text-destructive"
                    disabled={disabled}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
          {primaryManager && (
            <div className="text-xs text-muted-foreground">
              <Crown className="inline h-3 w-3 mr-1" />
              Primary manager: {primaryManager.name}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
