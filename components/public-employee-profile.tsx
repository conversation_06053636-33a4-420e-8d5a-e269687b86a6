"use client"

import { Employee } from "@/lib/types"
import { formatMultiDepartmentDisplay } from "@/lib/utils"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Building,
  Linkedin,
  Twitter,
  MessageCircle,
  Mail,
  User
} from "lucide-react"

interface PublicEmployeeProfileProps {
  employee: Employee
}

export function PublicEmployeeProfile({ employee }: PublicEmployeeProfileProps) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader className="pb-6">
            <div className="flex items-start gap-6">
              {/* Profile Avatar */}
              <div className="flex-shrink-0">
                <div className="w-20 h-20 rounded-full bg-primary flex items-center justify-center text-primary-foreground text-2xl font-bold">
                  {employee.firstName?.[0]?.toUpperCase()}{employee.lastName?.[0]?.toUpperCase()}
                </div>
              </div>

              {/* Basic Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-3xl font-bold text-foreground">
                    {employee.firstName} {employee.lastName}
                  </h1>
                </div>

                <div className="flex items-center gap-4 text-muted-foreground mb-4">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    <span>{formatMultiDepartmentDisplay(employee)}</span>
                  </div>
                  {employee.role && (
                    <>
                      <span>•</span>
                      <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                        {employee.role}
                      </Badge>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* About Section */}
            {employee.bio && (
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <User className="h-5 w-5 text-muted-foreground" />
                  <h2 className="text-lg font-semibold">About</h2>
                </div>
                <p className="text-muted-foreground leading-relaxed">
                  {employee.bio}
                </p>
              </div>
            )}

            {/* Social Links */}
            {(employee.linkedinUrl || employee.twitterUrl || employee.telegramUrl) && (
              <>
                <Separator />
                <div>
                  <p className="text-sm text-muted-foreground mb-3">Social Media</p>
                  <div className="flex flex-wrap gap-3">
                    {employee.linkedinUrl && (
                      <a
                        href={employee.linkedinUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 px-3 py-2 rounded-lg bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900 transition-all duration-200 hover:shadow-sm hover:scale-105"
                      >
                        <Linkedin className="h-4 w-4" />
                        <span className="text-sm font-medium">LinkedIn</span>
                      </a>
                    )}
                    {employee.twitterUrl && (
                      <a
                        href={employee.twitterUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 px-3 py-2 rounded-lg bg-sky-50 dark:bg-sky-950 text-sky-700 dark:text-sky-300 hover:bg-sky-100 dark:hover:bg-sky-900 transition-all duration-200 hover:shadow-sm hover:scale-105"
                      >
                        <Twitter className="h-4 w-4" />
                        <span className="text-sm font-medium">Twitter</span>
                      </a>
                    )}
                    {employee.telegramUrl && (
                      <a
                        href={employee.telegramUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 px-3 py-2 rounded-lg bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900 transition-all duration-200 hover:shadow-sm hover:scale-105"
                      >
                        <MessageCircle className="h-4 w-4" />
                        <span className="text-sm font-medium">Telegram</span>
                      </a>
                    )}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}