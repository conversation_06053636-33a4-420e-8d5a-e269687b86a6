"use client"

import * as React from "react"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, Filter, Download, Clock, Users, DollarSign, CheckCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { StatusBadge } from "@/components/status-badge"
import type { AccountingViewData, UserRole } from "@/lib/types"
import { toast } from "sonner"
import { announceToScreenReader, getTableAnnouncement } from "@/lib/accessibility"
import { markAsPaidAction } from "@/lib/actions"

interface AccountingDashboardTableProps {
  data: AccountingViewData[]
  currentUserRole?: UserRole
  onRefresh?: () => void
  onMarkAsPaid?: (employeeId: string) => Promise<void>
}


// Helper function to get compensation badge color
function getCompensationBadgeColor(compensation: "hourly" | "monthly"): string {
  return compensation === "hourly" 
    ? "bg-blue-100 text-blue-800" 
    : "bg-purple-100 text-purple-800"
}

// Helper function to get payment status badge
function PaymentStatusBadge({ status }: { status: "ready-to-pay" | "contact-manager" | "senior-needed" | "not-started" | "paid" | null }) {
  if (!status) return <Badge variant="outline" className="bg-gray-100 text-gray-600">No Status</Badge>

  const config = {
    "ready-to-pay": {
      label: "Ready to Pay",
      className: "bg-emerald-100 text-emerald-800"
    },
    "contact-manager": {
      label: "Contact Manager",
      className: "bg-orange-100 text-orange-800"
    },
    "senior-needed": {
      label: "Senior Approval Needed",
      className: "bg-purple-100 text-purple-800"
    },
    "not-started": {
      label: "Not Started",
      className: "bg-gray-100 text-gray-800"  
    },
    "paid": {
      label: "Paid",
      className: "bg-green-100 text-green-800"
    }
  }

  const { label, className } = config[status]
  return (
    <Badge variant="outline" className={className}>
      {status === 'paid' && <CheckCircle className="h-3 w-3 mr-1" />}
      {label}
    </Badge>
  )
}

export function AccountingDashboardTable({ data, currentUserRole, onRefresh, onMarkAsPaid }: AccountingDashboardTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])

  // Define columns for the accounting table
  const columns: ColumnDef<AccountingViewData>[] = [
    {
      accessorKey: "employeeName",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium"
        >
          Employee
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("employeeName")}</div>
      ),
    },
    {
      accessorKey: "compensation",
      header: "Type",
      cell: ({ row }) => {
        const compensation = row.getValue("compensation") as "hourly" | "monthly"
        return (
          <Badge 
            variant="outline" 
            className={getCompensationBadgeColor(compensation)}
          >
            {compensation === "hourly" ? "Hourly" : "Monthly"}
          </Badge>
        )
      },
    },
    {
      accessorKey: "hours",
      header: "Hours",
      cell: ({ row }) => {
        const compensation = row.getValue("compensation") as "hourly" | "monthly"
        const hours = row.getValue("hours") as number
        
        if (compensation === "monthly") {
          return <span className="text-muted-foreground">N/A</span>
        }
        
        return <div className="font-mono">{hours}</div>
      },
    },
    {
      accessorKey: "paymentStatus",
      header: "Payment Status",
      cell: ({ row }) => {
        const status = row.getValue("paymentStatus") as "ready-to-pay" | "contact-manager" | "senior-needed" | "not-started" | "paid" | null
        return <PaymentStatusBadge status={status} />
      },
    },
    {
      accessorKey: "status",
      header: "Appraisal Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as any
        return <StatusBadge status={status} />
      },
    },
    {
      accessorKey: "managerName",
      header: "Manager",
      cell: ({ row }) => (
        <div className="text-sm">{row.getValue("managerName")}</div>
      ),
    },
    {
      accessorKey: "submittedAt",
      header: "Submitted",
      cell: ({ row }) => {
        const submittedAt = row.getValue("submittedAt") as string | null
        if (!submittedAt) return <span className="text-muted-foreground">N/A</span>
        
        return (
          <div className="text-sm text-muted-foreground">
            {new Date(submittedAt).toLocaleDateString()}
          </div>
        )
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const employee = row.original
        const canMarkAsPaid = currentUserRole === 'accountant' && 
                             employee.paymentStatus === 'ready-to-pay' && 
                             !employee.isPaid

        const handleMarkAsPaid = async () => {
          try {
            if (onMarkAsPaid) {
              await onMarkAsPaid(employee.employeeId)
            } else {
              const result = await markAsPaidAction(employee.employeeId)
              if (result.success) {
                toast.success(result.message || 'Payment marked as paid successfully')
                onRefresh?.()
              } else {
                toast.error(result.error || 'Failed to mark payment as paid')
              }
            }
          } catch (error) {
            console.error('Error marking payment as paid:', error)
            toast.error('An unexpected error occurred')
          }
        }

        if (employee.isPaid || employee.paymentStatus === 'paid') {
          return (
            <Badge variant="outline" className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Paid
            </Badge>
          )
        }

        if (canMarkAsPaid) {
          return (
            <Button
              size="sm"
              onClick={handleMarkAsPaid}
              className="bg-green-600 hover:bg-green-700"
            >
              <DollarSign className="h-4 w-4 mr-1" />
              Mark as Paid
            </Button>
          )
        }

        return <span className="text-muted-foreground text-sm">N/A</span>
      },
    },
  ]

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: { sorting, columnFilters },
  })

  // CSV Export function
  const handleExport = () => {
    console.log('[ACCOUNTING] Starting CSV export process')
    
    // Filter for employees ready to pay or with submitted appraisals
    const exportableData = data.filter((item) => 
      item.paymentStatus === "ready-to-pay" || 
      (item.status === "submitted" && !item.paymentStatus)
    )
    
    if (exportableData.length === 0) {
      toast.error("No employees ready for payment to export.")
      announceToScreenReader("No employees ready for payment available for export", "assertive")
      return
    }

    // Create CSV headers - conditional based on compensation type
    const headers = ["Employee", "Compensation Type", "Hours", "Manager", "Submitted Date"]
    
    const csvRows = [
      headers.join(","),
      ...exportableData.map((row) => {
        const sanitizeCell = (value: any) => {
          if (value === null || value === undefined) return ""
          const stringValue = String(value)
          return stringValue.includes(",") ? `"${stringValue.replace(/"/g, '""')}"` : stringValue
        }

        return [
          sanitizeCell(row.employeeName),
          sanitizeCell(row.compensation === "hourly" ? "Hourly" : "Monthly"),
          sanitizeCell(row.compensation === "hourly" ? row.hours : "N/A"),
          sanitizeCell(row.managerName),
          sanitizeCell(row.submittedAt ? new Date(row.submittedAt).toLocaleDateString() : ""),
        ].join(",")
      }),
    ]

    const csvString = csvRows.join("\n")
    const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob)
      link.setAttribute("href", url)
      link.setAttribute("download", `accounting-export-${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = "hidden"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      toast.success(`Exported ${exportableData.length} employees ready for payment`)
      announceToScreenReader(`Successfully exported ${exportableData.length} employees to CSV file`, "polite")
    } else {
      toast.error("CSV export is not supported in this browser.")
      announceToScreenReader("CSV export failed - browser not supported", "assertive")
    }
  }

  return (
    <div className="space-y-4">
      {/* Filters and Export */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Input
            placeholder="Filter employees..."
            value={(table.getColumn("employeeName")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("employeeName")?.setFilterValue(event.target.value)
            }
            className="max-w-sm"
          />
          
          <Select
            value={(table.getColumn("compensation")?.getFilterValue() as string) ?? ""}
            onValueChange={(value) =>
              table.getColumn("compensation")?.setFilterValue(value === "all" ? "" : value)
            }
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="hourly">Hourly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={(table.getColumn("paymentStatus")?.getFilterValue() as string) ?? ""}
            onValueChange={(value) =>
              table.getColumn("paymentStatus")?.setFilterValue(value === "all" ? "" : value)
            }
          >
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Payment Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="ready-to-pay">Ready to Pay</SelectItem>
              <SelectItem value="contact-manager">Contact Manager</SelectItem>
              <SelectItem value="senior-needed">Senior Approval Needed</SelectItem>
              <SelectItem value="not-started">Not Started</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button
          onClick={handleExport}
          variant="outline"
          size="sm"
          aria-label="Export accounting data to CSV file"
        >
          <Download className="mr-2 h-4 w-4" aria-hidden="true" />
          Export CSV
        </Button>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table
          aria-label={getTableAnnouncement(data.length, columns.length)}
          role="table"
        >
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} role="row">
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} role="columnheader">
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow key={row.id} role="row" aria-rowindex={index + 2}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} role="gridcell">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No employees found for accounting.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} employee(s) shown.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}
