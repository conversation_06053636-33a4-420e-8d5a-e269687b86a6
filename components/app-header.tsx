"use client"

import React from "react"
import { usePathname } from "next/navigation"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { useIsMobile } from "@/components/ui/use-mobile"

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"


// A helper to capitalize strings
const capitalize = (s: string) => s.charAt(0).toUpperCase() + s.slice(1)

export function AppHeader() {
  const pathname = usePathname()
  const segments = pathname.split("/").filter(Boolean)
  const isMobile = useIsMobile()

  return (
    <header className="sticky top-0 z-10 flex h-14 sm:h-16 shrink-0 items-center gap-2 sm:gap-4 border-b bg-background px-3 sm:px-4 lg:px-6">
      <SidebarTrigger className="min-h-[44px] min-w-[44px] touch-manipulation" />
      <Separator orientation="vertical" className="h-4 sm:h-6" />
      <Breadcrumb className="flex-1 min-w-0">
        <BreadcrumbList className="flex-wrap">
          {segments.map((segment, index) => {
            const href = "/" + segments.slice(0, index + 1).join("/")
            const isLast = index === segments.length - 1
            // A simple check to see if it's a UUID or something we shouldn't display
            const isDynamicSegment = segment.length > 20 || !isNaN(Number.parseInt(segment))

            // On mobile, only show the last 2 segments to save space
            if (isMobile && index < segments.length - 2 && !isDynamicSegment) {
              return null
            }

            if (isDynamicSegment && isLast) {
              return (
                <React.Fragment key={href}>
                  <BreadcrumbSeparator className="hidden sm:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage className="mobile-body truncate">Appraisal</BreadcrumbPage>
                  </BreadcrumbItem>
                </React.Fragment>
              )
            }

            if (isDynamicSegment) return null

            return (
              <React.Fragment key={href}>
                <BreadcrumbItem>
                  {isLast ? (
                    <BreadcrumbPage className="mobile-body truncate">{capitalize(segment)}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink
                      href={href}
                      className="mobile-body truncate hover:text-foreground transition-colors touch-target flex items-center"
                    >
                      {capitalize(segment)}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {!isLast && <BreadcrumbSeparator className="hidden sm:block" />}
              </React.Fragment>
            )
          })}
        </BreadcrumbList>
      </Breadcrumb>

    </header>
  )
}
