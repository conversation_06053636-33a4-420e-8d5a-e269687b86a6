"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON><PERSON><PERSON>, Department, Manager, EmployeeManager } from "@/lib/types"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import { updateEmployeeProfile, assignManagersToEmployeeAction } from "@/lib/actions/employees"
import { useRouter } from "next/navigation"
import { Loader2 } from "lucide-react"
import { MultiManagerSelector } from "./multi-manager-selector"

// Selected manager interface for MultiManagerSelector
interface SelectedManager {
  id: string
  name: string
  isPrimary: boolean
}

const profileFormSchema = z.object({
  firstName: z.string().min(1, "First name is required").max(255),
  lastName: z.string().min(1, "Last name is required").max(255),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  role: z.string().max(100, "Role must be less than 100 characters").optional(),
  bio: z.string().max(1000, "Bio must be less than 1000 characters").optional(),
  linkedinUrl: z.string().url("Invalid LinkedIn URL").optional().or(z.literal("")),
  twitterUrl: z.string().url("Invalid Twitter/X URL").optional().or(z.literal("")),
  telegramUrl: z.string().url("Invalid Telegram URL").optional().or(z.literal("")),
  // Admin-only fields
  departmentId: z.string().uuid("Invalid department selected").optional(),
  // Removed managerId - now handled by MultiManagerSelector
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

interface EmployeeProfileFormProps {
  employee: Employee
  departments?: Department[]
  managers?: Manager[]
  isAdmin?: boolean
}

export function EmployeeProfileForm({ employee, departments = [], managers = [], isAdmin = false }: EmployeeProfileFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedManagers, setSelectedManagers] = useState<SelectedManager[]>([])
  const [isLoadingManagers, setIsLoadingManagers] = useState(true)

  console.log('🔧 [PROFILE FORM] Initializing with employee:', employee.id, employee.fullName)
  console.log('🔧 [PROFILE FORM] Available managers:', managers.length)
  console.log('🔧 [PROFILE FORM] Is admin:', isAdmin)

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      firstName: employee.firstName || "",
      lastName: employee.lastName || "",
      email: employee.email || "",
      role: employee.role || "",
      bio: employee.bio || "",
      linkedinUrl: employee.linkedinUrl || "",
      twitterUrl: employee.twitterUrl || "",
      telegramUrl: employee.telegramUrl || "",
      departmentId: employee.departmentId || "",
    },
  })

  // Load existing manager relationships on component mount
  useEffect(() => {
    async function loadExistingManagers() {
      if (!isAdmin) {
        setIsLoadingManagers(false)
        return
      }

      console.log('🔧 [PROFILE FORM] Loading existing managers for employee:', employee.id)

      try {
        // Load existing manager relationships from appy_employee_managers table
        const response = await fetch(`/api/employees/${employee.id}/managers`)
        if (response.ok) {
          const existingManagers: EmployeeManager[] = await response.json()
          console.log('🔧 [PROFILE FORM] Loaded existing managers:', existingManagers)

          // Convert to SelectedManager format
          const selectedManagersData: SelectedManager[] = existingManagers.map(em => ({
            id: em.managerId,
            name: em.managerName || managers.find(m => m.id === em.managerId)?.fullName || 'Unknown Manager',
            isPrimary: em.isPrimary
          }))

          setSelectedManagers(selectedManagersData)
          console.log('🔧 [PROFILE FORM] Set selected managers:', selectedManagersData)
        } else {
          console.warn('🔧 [PROFILE FORM] Failed to load existing managers, falling back to legacy manager_id')

          // Fallback: Use legacy manager_id if available
          if (employee.managerId && employee.managerId !== 'none') {
            const legacyManager = managers.find(m => m.id === employee.managerId)
            if (legacyManager) {
              setSelectedManagers([{
                id: legacyManager.id,
                name: legacyManager.fullName,
                isPrimary: true
              }])
              console.log('🔧 [PROFILE FORM] Using legacy manager:', legacyManager.fullName)
            }
          }
        }
      } catch (error) {
        console.error('🔧 [PROFILE FORM] Error loading existing managers:', error)

        // Fallback: Use legacy manager_id if available
        if (employee.managerId && employee.managerId !== 'none') {
          const legacyManager = managers.find(m => m.id === employee.managerId)
          if (legacyManager) {
            setSelectedManagers([{
              id: legacyManager.id,
              name: legacyManager.fullName,
              isPrimary: true
            }])
            console.log('🔧 [PROFILE FORM] Using legacy manager as fallback:', legacyManager.fullName)
          }
        }
      } finally {
        setIsLoadingManagers(false)
      }
    }

    loadExistingManagers()
  }, [employee.id, employee.managerId, managers, isAdmin])

  async function onSubmit(data: ProfileFormValues) {
    setIsSubmitting(true)
    console.log('🔧 [PROFILE FORM] Starting form submission for employee:', employee.id)
    console.log('🔧 [PROFILE FORM] Selected managers:', selectedManagers)

    try {
      const updateData: any = {
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.email || null,
        role: data.role || null,
        bio: data.bio || null,
        linkedin_url: data.linkedinUrl || null,
        twitter_url: data.twitterUrl || null,
        telegram_url: data.telegramUrl || null,
      }

      // Add admin-only fields if user is admin
      if (isAdmin) {
        updateData.department_id = data.departmentId || null

        // Set legacy manager_id to primary manager for backward compatibility
        const primaryManager = selectedManagers.find(m => m.isPrimary)
        updateData.manager_id = primaryManager ? primaryManager.id : null
        console.log('🔧 [PROFILE FORM] Setting legacy manager_id to:', updateData.manager_id)
      }

      // Step 1: Update employee profile
      console.log('🔧 [PROFILE FORM] Updating employee profile...')
      const result = await updateEmployeeProfile(employee.id, updateData)

      if (!result.success) {
        toast.error("error" in result ? result.error : "Failed to update profile")
        return
      }

      // Step 2: Update manager assignments (admin only)
      if (isAdmin && selectedManagers.length > 0) {
        console.log('🔧 [PROFILE FORM] Updating manager assignments...')

        const managerIds = selectedManagers.map(m => m.id)
        const primaryManagerId = selectedManagers.find(m => m.isPrimary)?.id

        const managerResult = await assignManagersToEmployeeAction(
          employee.id,
          managerIds,
          primaryManagerId
        )

        if (!managerResult.success) {
          console.error('🔧 [PROFILE FORM] Failed to assign managers:', managerResult.error)
          toast.error(`Profile updated but failed to assign managers: ${managerResult.error}`)
          return
        }

        console.log('🔧 [PROFILE FORM] Successfully assigned managers')
        toast.success(`Profile updated successfully with ${selectedManagers.length} manager(s) assigned`)
      } else {
        console.log('🔧 [PROFILE FORM] Profile updated successfully (no manager changes)')
        toast.success("Profile updated successfully")
      }

      router.push(`/dashboard/employees/${employee.id}/profile`)
    } catch (error) {
      console.error("🔧 [PROFILE FORM] Error updating profile:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Update your personal information and contact details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input type="email" {...field} />
                  </FormControl>
                  <FormDescription>
                    Your professional email address
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem className="md:col-span-2">
                  <FormLabel>Role/Position</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. Software Engineer, Marketing Manager" {...field} />
                  </FormControl>
                  <FormDescription>
                    Your job title or position
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem className="md:col-span-2">
                  <FormLabel>Bio</FormLabel>
                  <FormControl>
                    <Textarea 
                      {...field} 
                      rows={4}
                      placeholder="Tell us about yourself..."
                    />
                  </FormControl>
                  <FormDescription>
                    A brief description about yourself (max 1000 characters)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Social Links</CardTitle>
            <CardDescription>
              Add your professional social media profiles
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="linkedinUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>LinkedIn Profile</FormLabel>
                  <FormControl>
                    <Input 
                      type="url" 
                      placeholder="https://linkedin.com/in/username" 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Your LinkedIn profile URL
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="twitterUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>X (Twitter) Profile</FormLabel>
                  <FormControl>
                    <Input 
                      type="url" 
                      placeholder="https://twitter.com/username" 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Your X (Twitter) profile URL
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="telegramUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Telegram Profile</FormLabel>
                  <FormControl>
                    <Input 
                      type="url" 
                      placeholder="https://t.me/username" 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Your Telegram profile URL
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Admin-only fields */}
        {isAdmin && (
          <Card>
            <CardHeader>
              <CardTitle>Organization Assignment</CardTitle>
              <CardDescription>
                Manage department and manager assignments (Admin only)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="departmentId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Department</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select department" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {departments.map((dept) => (
                            <SelectItem key={dept.id} value={dept.id}>
                              {dept.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Employee's department assignment
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Manager Assignment
                  </label>
                  {isLoadingManagers ? (
                    <div className="flex items-center justify-center p-4 border rounded-md">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      <span className="text-sm text-muted-foreground">Loading managers...</span>
                    </div>
                  ) : (
                    <MultiManagerSelector
                      managers={managers}
                      selectedManagers={selectedManagers}
                      onManagersChange={setSelectedManagers}
                      disabled={isSubmitting}
                      placeholder="Select managers for this employee..."
                      className="w-full"
                    />
                  )}
                  <p className="text-sm text-muted-foreground">
                    Assign one or more managers to this employee. The first manager selected becomes the primary manager.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push(`/dashboard/employees/${employee.id}/profile`)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Save Changes
          </Button>
        </div>
      </form>
    </Form>
  )
}